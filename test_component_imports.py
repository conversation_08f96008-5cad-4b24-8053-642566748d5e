#!/usr/bin/env python3
"""
Test script to verify all critical components can be imported successfully
"""

import sys
import traceback

def test_import(module_name, import_statement):
    """Test a specific import and report results"""
    try:
        exec(import_statement)
        print(f"✅ SUCCESS: {module_name}")
        return True
    except Exception as e:
        print(f"❌ FAILED: {module_name}")
        print(f"   Error: {e}")
        print(f"   Import: {import_statement}")
        return False

def main():
    """Test all critical imports"""
    print("🧪 Testing AutoGPT Trading System Component Imports...")
    print("=" * 60)
    
    success_count = 0
    total_tests = 0
    
    # Test critical imports
    tests = [
        ("Neural Strategy Manager", "from src.strategies.neural_strategy_manager import get_neural_strategy_manager"),
        ("MarketContext", "from src.strategies.base import MarketContext"),
        ("SignalData", "from src.strategies.base import SignalData"),
        ("HFTMomentumStrategy", "from src.strategies.hft_momentum import HFTMomentumStrategy"),
        ("BaseStrategy", "from src.strategies.base import BaseStrategy"),
        ("TradeSignal", "from src.strategies.base import TradeSignal"),
    ]
    
    for test_name, import_stmt in tests:
        total_tests += 1
        if test_import(test_name, import_stmt):
            success_count += 1
        print()
    
    # Test neural strategy manager instantiation
    print("🧠 Testing Neural Strategy Manager Instantiation...")
    try:
        from src.strategies.neural_strategy_manager import get_neural_strategy_manager
        manager = get_neural_strategy_manager()
        print(f"✅ SUCCESS: Neural Strategy Manager instantiated")
        print(f"   Manager type: {type(manager)}")
        success_count += 1
    except Exception as e:
        print(f"❌ FAILED: Neural Strategy Manager instantiation")
        print(f"   Error: {e}")
    total_tests += 1
    print()
    
    # Test MarketContext creation
    print("📊 Testing MarketContext Creation...")
    try:
        from src.strategies.base import MarketContext
        from datetime import datetime
        
        context = MarketContext(
            price_data={},
            order_books={},
            sentiment_data={},
            technical_indicators={},
            volume_data={},
            news_data=[],
            arbitrage_opportunities=[],
            portfolio_state={},
            risk_metrics={},
            timestamp=datetime.now()
        )
        print(f"✅ SUCCESS: MarketContext created")
        print(f"   Context type: {type(context)}")
        success_count += 1
    except Exception as e:
        print(f"❌ FAILED: MarketContext creation")
        print(f"   Error: {e}")
    total_tests += 1
    print()
    
    # Test HFT Strategy instantiation
    print("🚀 Testing HFT Strategy Instantiation...")
    try:
        from src.strategies.hft_momentum import HFTMomentumStrategy
        
        strategy = HFTMomentumStrategy({
            'alpha': 0.3,
            'risk_multiplier': 1.5,
            'max_position': 0.05,
            'min_spread': 0.0001,
            'base_size': 0.01
        })
        print(f"✅ SUCCESS: HFTMomentumStrategy instantiated")
        print(f"   Strategy type: {type(strategy)}")
        success_count += 1
    except Exception as e:
        print(f"❌ FAILED: HFTMomentumStrategy instantiation")
        print(f"   Error: {e}")
    total_tests += 1
    print()
    
    # Summary
    print("=" * 60)
    print(f"🎯 IMPORT TEST RESULTS:")
    print(f"   ✅ Successful: {success_count}/{total_tests}")
    print(f"   ❌ Failed: {total_tests - success_count}/{total_tests}")
    print(f"   📊 Success Rate: {(success_count/total_tests)*100:.1f}%")
    
    if success_count == total_tests:
        print("\n🎉 ALL IMPORTS SUCCESSFUL! AutoGPT Trading System is ready!")
        return True
    else:
        print(f"\n⚠️  {total_tests - success_count} import(s) failed. System needs fixes.")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n💥 CRITICAL ERROR in test script: {e}")
        traceback.print_exc()
        sys.exit(1)
