"""
Normal Market Conditions Trading Agent with Quantum-Enhanced Features
Advanced agent optimized for stable, predictable market conditions
"""
import logging
import numpy as np
import asyncio
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import deque
from enum import Enum

# Quantum-inspired libraries
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
    from sklearn.neural_network import MLPRegressor
    from sklearn.preprocessing import StandardScaler
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

logger = logging.getLogger(__name__)

class MarketState(Enum):
    """Market state classifications for normal conditions"""
    STABLE_UPTREND = "stable_uptrend"
    STABLE_DOWNTREND = "stable_downtrend"
    SIDEWAYS = "sideways"
    LOW_VOLATILITY = "low_volatility"
    ACCUMULATION = "accumulation"
    DISTRIBUTION = "distribution"

class TradeAction(Enum):
    """Trading action types"""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"
    SCALE_IN = "scale_in"
    SCALE_OUT = "scale_out"

@dataclass
class TradingSignal:
    """Trading signal for normal market conditions"""
    action: TradeAction
    symbol: str
    confidence: float
    position_size: float
    entry_price: float
    stop_loss: float
    take_profit: float
    holding_period: int  # Expected holding period in minutes
    risk_score: float
    reasoning: str
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class MarketAnalysis:
    """Market analysis for normal conditions"""
    trend_direction: str
    trend_strength: float
    support_levels: List[float]
    resistance_levels: List[float]
    volatility: float
    momentum: float
    volume_profile: Dict[str, float]
    market_state: MarketState
    confidence: float

class QuantumNormalProcessor(nn.Module if TORCH_AVAILABLE else object):
    """Quantum-inspired processor for normal market conditions"""
    
    def __init__(self, input_size: int = 20, hidden_size: int = 64):
        if TORCH_AVAILABLE:
            super().__init__()
            self.input_size = input_size
            self.hidden_size = hidden_size
            
            # Normal market pattern recognition layers
            self.trend_analyzer = nn.Sequential(
                nn.Linear(input_size, hidden_size),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(hidden_size, hidden_size // 2),
                nn.ReLU(),
                nn.Linear(hidden_size // 2, 8)  # Trend features
            )
            
            # Support/Resistance detection
            self.level_detector = nn.Sequential(
                nn.Linear(input_size, hidden_size),
                nn.Tanh(),
                nn.Linear(hidden_size, 10)  # Key levels
            )
            
            # Signal generation
            self.signal_generator = nn.Sequential(
                nn.Linear(18, hidden_size),  # 8 trend + 10 levels
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(hidden_size, 5)  # Action probabilities
            )
            
            self.optimizer = optim.Adam(self.parameters(), lr=0.001)
        else:
            # Fallback for when PyTorch is not available
            self.input_size = input_size
            self.hidden_size = hidden_size
            logger.warning("PyTorch not available, using fallback implementation")
    
    def forward(self, x) -> Dict[str, Any]:
        """Forward pass for normal market analysis"""
        if not TORCH_AVAILABLE:
            return self._fallback_analysis(x)
            
        try:
            if isinstance(x, np.ndarray):
                x = torch.tensor(x, dtype=torch.float32)
            
            # Analyze trend patterns
            trend_features = self.trend_analyzer(x)
            
            # Detect support/resistance levels
            level_features = self.level_detector(x)
            
            # Generate trading signal
            combined_features = torch.cat([trend_features, level_features], dim=-1)
            signal_probs = torch.softmax(self.signal_generator(combined_features), dim=-1)
            
            return {
                'trend_features': trend_features.detach().numpy(),
                'level_features': level_features.detach().numpy(),
                'signal_probabilities': signal_probs.detach().numpy(),
                'confidence': float(torch.max(signal_probs))
            }
            
        except Exception as e:
            logger.error(f"Error in quantum normal processor: {e}")
            return self._fallback_analysis(x)
    
    def _fallback_analysis(self, x) -> Dict[str, Any]:
        """Fallback analysis when PyTorch is not available"""
        try:
            if isinstance(x, torch.Tensor):
                x = x.numpy()
            
            # Simple statistical analysis
            trend_score = np.mean(np.diff(x[-10:]))  # Recent trend
            volatility = np.std(x[-20:])  # Recent volatility
            
            # Generate simple signal
            if trend_score > volatility * 0.5:
                action_probs = [0.1, 0.6, 0.2, 0.05, 0.05]  # Buy bias
            elif trend_score < -volatility * 0.5:
                action_probs = [0.6, 0.1, 0.2, 0.05, 0.05]  # Sell bias
            else:
                action_probs = [0.2, 0.2, 0.5, 0.05, 0.05]  # Hold bias
            
            return {
                'trend_features': np.array([trend_score, volatility] + [0] * 6),
                'level_features': np.array([0] * 10),
                'signal_probabilities': np.array(action_probs),
                'confidence': max(action_probs)
            }
            
        except Exception as e:
            logger.error(f"Error in fallback analysis: {e}")
            return {
                'trend_features': np.zeros(8),
                'level_features': np.zeros(10),
                'signal_probabilities': np.array([0.2, 0.2, 0.6, 0.0, 0.0]),
                'confidence': 0.6
            }

class NormalAgent:
    """
    Trading agent optimized for normal market conditions
    Features:
    - Trend following with momentum analysis
    - Support/resistance level trading
    - Position scaling strategies
    - Risk management for stable markets
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # Initialize quantum processor
        self.quantum_processor = QuantumNormalProcessor()
        
        # Market analysis components
        self.price_history = deque(maxlen=200)
        self.volume_history = deque(maxlen=200)
        self.support_resistance_levels = {}
        
        # Trading parameters for normal conditions
        self.min_confidence = self.config.get('min_confidence', 0.65)
        self.max_position_size = self.config.get('max_position_size', 0.15)  # 15% of portfolio
        self.stop_loss_pct = self.config.get('stop_loss_pct', 0.02)  # 2% stop loss
        self.take_profit_pct = self.config.get('take_profit_pct', 0.04)  # 4% take profit
        self.trend_threshold = self.config.get('trend_threshold', 0.01)  # 1% trend threshold
        
        # Performance tracking
        self.trade_history = []
        self.success_rate = 0.0
        self.avg_holding_period = 60  # minutes
        
        # ML models for normal market conditions
        self.trend_predictor = None
        self.level_predictor = None
        self._initialize_ml_models()
        
        logger.info("🎯 [NORMAL-AGENT] Normal market conditions agent initialized")
    
    def _initialize_ml_models(self):
        """Initialize ML models for normal market analysis"""
        try:
            if TORCH_AVAILABLE:
                # Initialize ensemble models for different aspects
                self.trend_predictor = RandomForestRegressor(
                    n_estimators=50,
                    max_depth=10,
                    random_state=42
                )
                
                self.level_predictor = GradientBoostingRegressor(
                    n_estimators=100,
                    learning_rate=0.1,
                    random_state=42
                )
                
                self.scaler = StandardScaler()
                
            logger.info("✅ [NORMAL-AGENT] ML models initialized")
            
        except Exception as e:
            logger.error(f"❌ [NORMAL-AGENT] Error initializing ML models: {e}")
    
    async def analyze_market_conditions(self, market_data: Dict[str, Any]) -> MarketAnalysis:
        """Analyze market conditions for normal trading"""
        try:
            symbol = market_data.get('symbol', 'UNKNOWN')
            prices = market_data.get('prices', [])
            volumes = market_data.get('volumes', [])
            
            if len(prices) < 20:
                logger.warning(f"⚠️ [NORMAL-AGENT] Insufficient price data for {symbol}")
                return self._default_market_analysis()
            
            # Update price and volume history
            self.price_history.extend(prices[-20:])
            if volumes:
                self.volume_history.extend(volumes[-20:])
            
            # Analyze trend
            trend_analysis = await self._analyze_trend(prices)
            
            # Detect support and resistance levels
            levels = await self._detect_support_resistance(prices)
            
            # Calculate volatility and momentum
            volatility = self._calculate_volatility(prices)
            momentum = self._calculate_momentum(prices)
            
            # Determine market state
            market_state = self._determine_market_state(trend_analysis, volatility, momentum)
            
            # Create market analysis
            analysis = MarketAnalysis(
                trend_direction=trend_analysis['direction'],
                trend_strength=trend_analysis['strength'],
                support_levels=levels['support'],
                resistance_levels=levels['resistance'],
                volatility=volatility,
                momentum=momentum,
                volume_profile=self._analyze_volume_profile(volumes) if volumes else {},
                market_state=market_state,
                confidence=self._calculate_analysis_confidence(trend_analysis, levels, volatility)
            )
            
            logger.debug(f"📊 [NORMAL-AGENT] Market analysis for {symbol}: "
                        f"trend={analysis.trend_direction}, state={analysis.market_state.value}")
            
            return analysis
            
        except Exception as e:
            logger.error(f"❌ [NORMAL-AGENT] Error analyzing market conditions: {e}")
            return self._default_market_analysis()
    
    async def generate_trading_signal(self, market_data: Dict[str, Any], 
                                    market_analysis: MarketAnalysis) -> Optional[TradingSignal]:
        """Generate trading signal for normal market conditions"""
        try:
            symbol = market_data.get('symbol', 'UNKNOWN')
            current_price = market_data.get('current_price', 0)
            
            if current_price <= 0:
                logger.warning(f"⚠️ [NORMAL-AGENT] Invalid price for {symbol}: {current_price}")
                return None
            
            # Use quantum processor for signal generation
            price_features = np.array(list(self.price_history)[-20:])
            if len(price_features) < 20:
                price_features = np.pad(price_features, (20 - len(price_features), 0), 'edge')
            
            quantum_output = self.quantum_processor.forward(price_features)
            signal_probs = quantum_output['signal_probabilities']
            confidence = quantum_output['confidence']
            
            # Map probabilities to actions
            actions = [TradeAction.SELL, TradeAction.BUY, TradeAction.HOLD, 
                      TradeAction.SCALE_IN, TradeAction.SCALE_OUT]
            
            # Select action with highest probability
            action_idx = np.argmax(signal_probs)
            selected_action = actions[action_idx]
            
            # Check confidence threshold
            if confidence < self.min_confidence:
                selected_action = TradeAction.HOLD
                confidence = 0.5
            
            # Skip if action is hold
            if selected_action == TradeAction.HOLD:
                return None
            
            # Calculate position parameters
            position_size = self._calculate_position_size(market_analysis, confidence)
            stop_loss = self._calculate_stop_loss(current_price, selected_action, market_analysis)
            take_profit = self._calculate_take_profit(current_price, selected_action, market_analysis)
            holding_period = self._estimate_holding_period(market_analysis)
            risk_score = self._calculate_risk_score(market_analysis, selected_action)
            
            # Generate reasoning
            reasoning = self._generate_signal_reasoning(selected_action, market_analysis, confidence)
            
            signal = TradingSignal(
                action=selected_action,
                symbol=symbol,
                confidence=confidence,
                position_size=position_size,
                entry_price=current_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                holding_period=holding_period,
                risk_score=risk_score,
                reasoning=reasoning
            )
            
            logger.info(f"🎯 [NORMAL-AGENT] Generated signal for {symbol}: "
                       f"{selected_action.value} @ {current_price:.4f} (confidence: {confidence:.3f})")
            
            return signal
            
        except Exception as e:
            logger.error(f"❌ [NORMAL-AGENT] Error generating trading signal: {e}")
            return None
    
    async def _analyze_trend(self, prices: List[float]) -> Dict[str, Any]:
        """Analyze price trend"""
        try:
            if len(prices) < 10:
                return {'direction': 'sideways', 'strength': 0.0}
            
            # Short-term trend (last 10 periods)
            short_trend = np.polyfit(range(10), prices[-10:], 1)[0]
            
            # Medium-term trend (last 20 periods)
            if len(prices) >= 20:
                medium_trend = np.polyfit(range(20), prices[-20:], 1)[0]
            else:
                medium_trend = short_trend
            
            # Combine trends
            avg_trend = (short_trend + medium_trend) / 2
            
            # Determine direction
            if avg_trend > self.trend_threshold:
                direction = 'uptrend'
            elif avg_trend < -self.trend_threshold:
                direction = 'downtrend'
            else:
                direction = 'sideways'
            
            # Calculate strength (normalized)
            strength = min(1.0, abs(avg_trend) / (np.std(prices[-20:]) * 0.1))
            
            return {
                'direction': direction,
                'strength': strength,
                'short_trend': short_trend,
                'medium_trend': medium_trend
            }
            
        except Exception as e:
            logger.error(f"Error analyzing trend: {e}")
            return {'direction': 'sideways', 'strength': 0.0}
    
    async def _detect_support_resistance(self, prices: List[float]) -> Dict[str, List[float]]:
        """Detect support and resistance levels"""
        try:
            if len(prices) < 20:
                current_price = prices[-1] if prices else 100
                return {
                    'support': [current_price * 0.98],
                    'resistance': [current_price * 1.02]
                }
            
            # Use local minima and maxima
            support_levels = []
            resistance_levels = []
            
            # Find local extremes
            for i in range(2, len(prices) - 2):
                # Local minimum (support)
                if (prices[i] < prices[i-1] and prices[i] < prices[i-2] and
                    prices[i] < prices[i+1] and prices[i] < prices[i+2]):
                    support_levels.append(prices[i])
                
                # Local maximum (resistance)
                if (prices[i] > prices[i-1] and prices[i] > prices[i-2] and
                    prices[i] > prices[i+1] and prices[i] > prices[i+2]):
                    resistance_levels.append(prices[i])
            
            # Keep only recent and significant levels
            current_price = prices[-1]
            
            # Filter support levels (below current price)
            support_levels = [level for level in support_levels 
                            if level < current_price * 1.05 and level > current_price * 0.90]
            support_levels = sorted(support_levels, reverse=True)[:3]  # Top 3
            
            # Filter resistance levels (above current price)
            resistance_levels = [level for level in resistance_levels 
                               if level > current_price * 0.95 and level < current_price * 1.10]
            resistance_levels = sorted(resistance_levels)[:3]  # Top 3
            
            # Add default levels if none found
            if not support_levels:
                support_levels = [current_price * 0.98, current_price * 0.96]
            
            if not resistance_levels:
                resistance_levels = [current_price * 1.02, current_price * 1.04]
            
            return {
                'support': support_levels,
                'resistance': resistance_levels
            }
            
        except Exception as e:
            logger.error(f"Error detecting support/resistance: {e}")
            current_price = prices[-1] if prices else 100
            return {
                'support': [current_price * 0.98],
                'resistance': [current_price * 1.02]
            }
    
    def _calculate_volatility(self, prices: List[float]) -> float:
        """Calculate price volatility"""
        try:
            if len(prices) < 10:
                return 0.02  # Default 2%
            
            returns = np.diff(prices[-20:]) / prices[-21:-1]
            volatility = np.std(returns)
            
            return max(0.001, volatility)  # Minimum 0.1%
            
        except Exception as e:
            logger.error(f"Error calculating volatility: {e}")
            return 0.02
    
    def _calculate_momentum(self, prices: List[float]) -> float:
        """Calculate price momentum"""
        try:
            if len(prices) < 10:
                return 0.0
            
            # Rate of change momentum
            momentum = (prices[-1] - prices[-10]) / prices[-10]
            
            return momentum
            
        except Exception as e:
            logger.error(f"Error calculating momentum: {e}")
            return 0.0
    
    def _determine_market_state(self, trend_analysis: Dict, volatility: float, momentum: float) -> MarketState:
        """Determine current market state"""
        try:
            direction = trend_analysis['direction']
            strength = trend_analysis['strength']
            
            # Low volatility market
            if volatility < 0.01:
                return MarketState.LOW_VOLATILITY
            
            # Strong trends
            if strength > 0.7:
                if direction == 'uptrend':
                    return MarketState.STABLE_UPTREND
                elif direction == 'downtrend':
                    return MarketState.STABLE_DOWNTREND
            
            # Sideways market
            if direction == 'sideways' or strength < 0.3:
                return MarketState.SIDEWAYS
            
            # Momentum-based states
            if momentum > 0.02 and direction == 'uptrend':
                return MarketState.ACCUMULATION
            elif momentum < -0.02 and direction == 'downtrend':
                return MarketState.DISTRIBUTION
            
            # Default to sideways
            return MarketState.SIDEWAYS
            
        except Exception as e:
            logger.error(f"Error determining market state: {e}")
            return MarketState.SIDEWAYS
    
    def _analyze_volume_profile(self, volumes: List[float]) -> Dict[str, float]:
        """Analyze volume profile"""
        try:
            if not volumes or len(volumes) < 10:
                return {'avg_volume': 0, 'volume_trend': 0}
            
            recent_volumes = volumes[-10:]
            avg_volume = np.mean(recent_volumes)
            
            # Volume trend
            if len(volumes) >= 20:
                old_avg = np.mean(volumes[-20:-10])
                volume_trend = (avg_volume - old_avg) / old_avg if old_avg > 0 else 0
            else:
                volume_trend = 0
            
            return {
                'avg_volume': avg_volume,
                'volume_trend': volume_trend,
                'volume_spike': max(recent_volumes) / avg_volume if avg_volume > 0 else 1
            }
            
        except Exception as e:
            logger.error(f"Error analyzing volume profile: {e}")
            return {'avg_volume': 0, 'volume_trend': 0}
    
    def _calculate_analysis_confidence(self, trend_analysis: Dict, levels: Dict, volatility: float) -> float:
        """Calculate confidence in market analysis"""
        try:
            confidence = 0.5  # Base confidence
            
            # Add confidence from trend strength
            confidence += trend_analysis['strength'] * 0.3
            
            # Add confidence from clear support/resistance levels
            if len(levels['support']) > 0 and len(levels['resistance']) > 0:
                confidence += 0.2
            
            # Moderate volatility increases confidence
            if 0.01 <= volatility <= 0.03:
                confidence += 0.1
            elif volatility > 0.05:
                confidence -= 0.1
            
            return min(1.0, max(0.0, confidence))
            
        except Exception as e:
            logger.error(f"Error calculating analysis confidence: {e}")
            return 0.5
    
    def _default_market_analysis(self) -> MarketAnalysis:
        """Return default market analysis"""
        return MarketAnalysis(
            trend_direction='sideways',
            trend_strength=0.0,
            support_levels=[],
            resistance_levels=[],
            volatility=0.02,
            momentum=0.0,
            volume_profile={},
            market_state=MarketState.SIDEWAYS,
            confidence=0.3
        )
    
    def _calculate_position_size(self, market_analysis: MarketAnalysis, confidence: float) -> float:
        """Calculate position size based on market analysis and confidence"""
        try:
            # Base position size
            base_size = self.max_position_size * 0.5  # Start with 50% of max
            
            # Adjust for confidence
            confidence_multiplier = min(2.0, confidence * 2)
            
            # Adjust for volatility (lower size for higher volatility)
            volatility_multiplier = max(0.5, 1 - market_analysis.volatility * 10)
            
            # Adjust for trend strength
            trend_multiplier = 1 + market_analysis.trend_strength * 0.5
            
            position_size = base_size * confidence_multiplier * volatility_multiplier * trend_multiplier
            
            # Apply limits
            position_size = min(self.max_position_size, max(0.01, position_size))
            
            return position_size
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0.05  # Default 5%
    
    def _calculate_stop_loss(self, entry_price: float, action: TradeAction, 
                           market_analysis: MarketAnalysis) -> float:
        """Calculate stop loss level"""
        try:
            # Base stop loss percentage
            base_stop_pct = self.stop_loss_pct
            
            # Adjust for volatility
            volatility_adjustment = market_analysis.volatility * 2
            adjusted_stop_pct = base_stop_pct + volatility_adjustment
            
            # Use support/resistance levels for better stops
            if action == TradeAction.BUY and market_analysis.support_levels:
                # For buy orders, use nearest support as stop
                nearest_support = max([level for level in market_analysis.support_levels 
                                     if level < entry_price], default=None)
                if nearest_support:
                    support_stop = nearest_support * 0.99  # Slightly below support
                    calculated_stop = entry_price * (1 - adjusted_stop_pct)
                    return max(support_stop, calculated_stop)  # Use the higher stop
            
            elif action == TradeAction.SELL and market_analysis.resistance_levels:
                # For sell orders, use nearest resistance as stop
                nearest_resistance = min([level for level in market_analysis.resistance_levels 
                                        if level > entry_price], default=None)
                if nearest_resistance:
                    resistance_stop = nearest_resistance * 1.01  # Slightly above resistance
                    calculated_stop = entry_price * (1 + adjusted_stop_pct)
                    return min(resistance_stop, calculated_stop)  # Use the lower stop
            
            # Default percentage-based stop
            if action == TradeAction.BUY or action == TradeAction.SCALE_IN:
                return entry_price * (1 - adjusted_stop_pct)
            else:
                return entry_price * (1 + adjusted_stop_pct)
            
        except Exception as e:
            logger.error(f"Error calculating stop loss: {e}")
            if action == TradeAction.BUY or action == TradeAction.SCALE_IN:
                return entry_price * 0.98  # 2% stop loss
            else:
                return entry_price * 1.02
    
    def _calculate_take_profit(self, entry_price: float, action: TradeAction, 
                             market_analysis: MarketAnalysis) -> float:
        """Calculate take profit level"""
        try:
            # Base take profit percentage
            base_tp_pct = self.take_profit_pct
            
            # Adjust for trend strength
            trend_adjustment = market_analysis.trend_strength * 0.02
            adjusted_tp_pct = base_tp_pct + trend_adjustment
            
            # Use support/resistance levels for better targets
            if action == TradeAction.BUY and market_analysis.resistance_levels:
                # For buy orders, use nearest resistance as target
                nearest_resistance = min([level for level in market_analysis.resistance_levels 
                                        if level > entry_price], default=None)
                if nearest_resistance:
                    resistance_target = nearest_resistance * 0.99  # Slightly below resistance
                    calculated_target = entry_price * (1 + adjusted_tp_pct)
                    return min(resistance_target, calculated_target)  # Use the closer target
            
            elif action == TradeAction.SELL and market_analysis.support_levels:
                # For sell orders, use nearest support as target
                nearest_support = max([level for level in market_analysis.support_levels 
                                     if level < entry_price], default=None)
                if nearest_support:
                    support_target = nearest_support * 1.01  # Slightly above support
                    calculated_target = entry_price * (1 - adjusted_tp_pct)
                    return max(support_target, calculated_target)  # Use the closer target
            
            # Default percentage-based target
            if action == TradeAction.BUY or action == TradeAction.SCALE_IN:
                return entry_price * (1 + adjusted_tp_pct)
            else:
                return entry_price * (1 - adjusted_tp_pct)
            
        except Exception as e:
            logger.error(f"Error calculating take profit: {e}")
            if action == TradeAction.BUY or action == TradeAction.SCALE_IN:
                return entry_price * 1.04  # 4% take profit
            else:
                return entry_price * 0.96
    
    def _estimate_holding_period(self, market_analysis: MarketAnalysis) -> int:
        """Estimate optimal holding period in minutes"""
        try:
            # Base holding period for normal conditions
            base_period = self.avg_holding_period
            
            # Adjust for market state
            if market_analysis.market_state in [MarketState.STABLE_UPTREND, MarketState.STABLE_DOWNTREND]:
                return int(base_period * 1.5)  # Hold longer in strong trends
            elif market_analysis.market_state == MarketState.LOW_VOLATILITY:
                return int(base_period * 2)  # Hold longer in low volatility
            elif market_analysis.market_state == MarketState.SIDEWAYS:
                return int(base_period * 0.7)  # Shorter holds in sideways market
            
            return base_period
            
        except Exception as e:
            logger.error(f"Error estimating holding period: {e}")
            return 60  # Default 60 minutes
    
    def _calculate_risk_score(self, market_analysis: MarketAnalysis, action: TradeAction) -> float:
        """Calculate risk score for the trade"""
        try:
            risk_score = 0.5  # Base risk
            
            # Volatility risk
            risk_score += min(0.3, market_analysis.volatility * 10)
            
            # Trend alignment risk
            if ((action in [TradeAction.BUY, TradeAction.SCALE_IN] and 
                 market_analysis.trend_direction == 'downtrend') or
                (action in [TradeAction.SELL, TradeAction.SCALE_OUT] and 
                 market_analysis.trend_direction == 'uptrend')):
                risk_score += 0.2  # Counter-trend trades are riskier
            
            # Market state risk
            if market_analysis.market_state == MarketState.SIDEWAYS:
                risk_score += 0.1  # Sideways markets are unpredictable
            
            return min(1.0, risk_score)
            
        except Exception as e:
            logger.error(f"Error calculating risk score: {e}")
            return 0.6  # Default moderate risk
    
    def _generate_signal_reasoning(self, action: TradeAction, market_analysis: MarketAnalysis, 
                                 confidence: float) -> str:
        """Generate human-readable reasoning for the trading signal"""
        try:
            reasoning_parts = []
            
            # Market state reasoning
            reasoning_parts.append(f"Market state: {market_analysis.market_state.value}")
            
            # Trend reasoning
            if market_analysis.trend_strength > 0.5:
                reasoning_parts.append(f"Strong {market_analysis.trend_direction} trend (strength: {market_analysis.trend_strength:.2f})")
            
            # Action reasoning
            if action == TradeAction.BUY:
                reasoning_parts.append("Buy signal based on upward momentum and support levels")
            elif action == TradeAction.SELL:
                reasoning_parts.append("Sell signal based on downward momentum and resistance levels")
            elif action == TradeAction.SCALE_IN:
                reasoning_parts.append("Scale in position due to continued trend strength")
            elif action == TradeAction.SCALE_OUT:
                reasoning_parts.append("Scale out position to secure profits")
            
            # Confidence reasoning
            if confidence > 0.8:
                reasoning_parts.append("High confidence based on clear market patterns")
            elif confidence > 0.6:
                reasoning_parts.append("Moderate confidence with some uncertainty")
            else:
                reasoning_parts.append("Lower confidence due to mixed signals")
            
            return "; ".join(reasoning_parts)
            
        except Exception as e:
            logger.error(f"Error generating signal reasoning: {e}")
            return f"Signal generated for {action.value} with {confidence:.1%} confidence"
    
    async def update_from_trade_result(self, trade_result: Dict[str, Any]):
        """Update agent from trade execution results"""
        try:
            # Record trade in history
            self.trade_history.append({
                'timestamp': datetime.now(),
                'action': trade_result.get('action'),
                'symbol': trade_result.get('symbol'),
                'success': trade_result.get('success', False),
                'profit': trade_result.get('profit', 0.0),
                'confidence': trade_result.get('confidence', 0.0)
            })
            
            # Update success rate
            recent_trades = self.trade_history[-20:]  # Last 20 trades
            if recent_trades:
                self.success_rate = sum(1 for trade in recent_trades if trade['success']) / len(recent_trades)
            
            # Update average holding period if available
            if 'holding_period' in trade_result:
                self.avg_holding_period = int((self.avg_holding_period + trade_result['holding_period']) / 2)
            
            # Update quantum processor with learning
            if TORCH_AVAILABLE and hasattr(self.quantum_processor, 'optimizer'):
                await self._update_quantum_learning(trade_result)
            
            logger.debug(f"🎓 [NORMAL-AGENT] Updated from trade result: success_rate={self.success_rate:.2%}")
            
        except Exception as e:
            logger.error(f"❌ [NORMAL-AGENT] Error updating from trade result: {e}")
    
    async def _update_quantum_learning(self, trade_result: Dict[str, Any]):
        """Update quantum processor based on trade results"""
        try:
            if not TORCH_AVAILABLE:
                return
            
            # Create training data from trade result
            success = trade_result.get('success', False)
            profit = trade_result.get('profit', 0.0)
            
            # Convert to reward signal
            reward = 1.0 if success and profit > 0 else -1.0
            
            # Simple reward-based learning (would be more sophisticated in production)
            if hasattr(self.quantum_processor, 'optimizer'):
                # Pseudo-gradient update based on reward
                for param in self.quantum_processor.parameters():
                    if param.grad is not None:
                        param.data += 0.001 * reward * param.grad.sign()
            
        except Exception as e:
            logger.error(f"Error updating quantum learning: {e}")
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get current agent status and performance metrics"""
        try:
            return {
                'agent_type': 'normal_conditions',
                'success_rate': self.success_rate,
                'total_trades': len(self.trade_history),
                'avg_holding_period': self.avg_holding_period,
                'confidence_threshold': self.min_confidence,
                'max_position_size': self.max_position_size,
                'recent_performance': self.trade_history[-5:] if self.trade_history else []
            }
            
        except Exception as e:
            logger.error(f"Error getting agent status: {e}")
            return {'agent_type': 'normal_conditions', 'status': 'error'}

    async def decide_action(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Decide trading action based on current market state
        This method is called by MetaStrategyController
        
        Args:
            state: Current market state and data
            
        Returns:
            Dictionary containing trading decision
        """
        try:
            # Generate trading signal from market state
            signal = await self.generate_trading_signal(state)
            
            if signal is None:
                return {
                    'action': 'hold',
                    'confidence': 0.0,
                    'position_size': 0.0,
                    'reason': 'No valid signal generated',
                    'agent_type': 'normal_conditions'
                }
            
            # Convert signal to action decision
            action_decision = {
                'action': signal.action.value,
                'confidence': signal.confidence,
                'position_size': signal.position_size,
                'entry_price': signal.entry_price,
                'stop_loss': signal.stop_loss,
                'take_profit': signal.take_profit,
                'reasoning': signal.reasoning,
                'agent_type': 'normal_conditions',
                'market_state': signal.market_state.value if signal.market_state else 'unknown',
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"🎯 [NORMAL-AGENT] Decision: {action_decision['action']} "
                       f"(confidence: {action_decision['confidence']:.3f})")
            
            return action_decision
            
        except Exception as e:
            logger.error(f"❌ [NORMAL-AGENT] Error in decide_action: {e}")
            return {
                'action': 'hold',
                'confidence': 0.0,
                'position_size': 0.0,
                'reason': f'Error: {e}',
                'agent_type': 'normal_conditions'
            }

# Factory function for creating normal agent
def create_normal_agent(config: Dict[str, Any] = None) -> NormalAgent:
    """Factory function to create a normal market conditions agent"""
    return NormalAgent(config)

# Export classes and functions
__all__ = ['NormalAgent', 'TradingSignal', 'MarketAnalysis', 'MarketState', 'TradeAction', 'create_normal_agent']
