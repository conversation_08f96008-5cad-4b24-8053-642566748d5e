"""
Comprehensive Strategy Integrator for AutoGPT-Trader
Central coordination and integration of all trading strategies
"""
import logging
import asyncio
import numpy as np
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from collections import defaultdict, deque
from enum import Enum

logger = logging.getLogger(__name__)

class StrategyType(Enum):
    """Types of trading strategies"""
    TREND_FOLLOWING = "trend_following"
    MEAN_REVERSION = "mean_reversion"
    MOMENTUM = "momentum"
    ARBITRAGE = "arbitrage"
    MARKET_MAKING = "market_making"
    VOLATILITY = "volatility"
    SENTIMENT = "sentiment"
    NEURAL = "neural"
    QUANTUM = "quantum"

class SignalStrength(Enum):
    """Signal strength levels"""
    VERY_WEAK = 1
    WEAK = 2
    MODERATE = 3
    STRONG = 4
    VERY_STRONG = 5

@dataclass
class IntegratedSignal:
    """Integrated trading signal from multiple strategies"""
    symbol: str
    action: str  # 'buy', 'sell', 'hold'
    confidence: float
    strength: SignalStrength
    position_size: float
    entry_price: float
    stop_loss: float
    take_profit: float
    strategy_name: str
    contributing_strategies: List[str]
    consensus_score: float
    risk_score: float
    expected_return: float
    holding_period: int  # minutes
    reasoning: str
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class StrategyPerformance:
    """Performance metrics for individual strategies"""
    strategy_name: str
    success_rate: float
    avg_return: float
    total_trades: int
    profit_factor: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    avg_win: float
    avg_loss: float
    recent_performance: List[float]
    confidence_score: float
    last_updated: datetime = field(default_factory=datetime.now)

class ComprehensiveStrategyIntegrator:
    """
    Central coordinator for all trading strategies
    
    Features:
    - Multi-strategy signal integration
    - Adaptive strategy weighting
    - Performance-based strategy selection
    - Risk-adjusted position sizing
    - Strategy evolution and optimization
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # Strategy management
        self.active_strategies = {}
        self.strategy_weights = {}
        self.strategy_performance = {}
        self.signal_history = deque(maxlen=1000)
        self.execution_history = deque(maxlen=500)
        
        # Integration parameters
        self.min_consensus_threshold = self.config.get('min_consensus_threshold', 0.6)
        self.max_strategies_per_signal = self.config.get('max_strategies_per_signal', 5)
        self.performance_window = self.config.get('performance_window', 100)  # trades
        self.adaptation_rate = self.config.get('adaptation_rate', 0.1)
        
        # Risk management
        self.max_total_exposure = self.config.get('max_total_exposure', 0.8)
        self.max_single_position = self.config.get('max_single_position', 0.2)
        self.risk_budget = self.config.get('risk_budget', 0.02)  # 2% daily VaR
        
        # Initialize default strategy weights
        self._initialize_strategy_weights()
        
        logger.info("🎯 [STRATEGY-INTEGRATOR] Comprehensive Strategy Integrator initialized")
    
    def _initialize_strategy_weights(self):
        """Initialize default strategy weights"""
        self.strategy_weights = {
            StrategyType.NEURAL: 0.25,
            StrategyType.MOMENTUM: 0.20,
            StrategyType.TREND_FOLLOWING: 0.15,
            StrategyType.VOLATILITY: 0.15,
            StrategyType.SENTIMENT: 0.10,
            StrategyType.ARBITRAGE: 0.10,
            StrategyType.MARKET_MAKING: 0.05
        }
    
    def register_strategy(self, strategy_name: str, strategy_type: StrategyType, 
                         strategy_instance: Any):
        """Register a trading strategy"""
        try:
            self.active_strategies[strategy_name] = {
                'type': strategy_type,
                'instance': strategy_instance,
                'enabled': True,
                'last_signal': None,
                'signal_count': 0,
                'success_count': 0
            }
            
            # Initialize performance tracking
            self.strategy_performance[strategy_name] = StrategyPerformance(
                strategy_name=strategy_name,
                success_rate=0.5,  # Default
                avg_return=0.0,
                total_trades=0,
                profit_factor=1.0,
                max_drawdown=0.0,
                sharpe_ratio=0.0,
                win_rate=0.5,
                avg_win=0.0,
                avg_loss=0.0,
                recent_performance=[],
                confidence_score=0.5
            )
            
            logger.info(f"📋 [STRATEGY-INTEGRATOR] Registered strategy: {strategy_name} ({strategy_type.value})")
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY-INTEGRATOR] Error registering strategy {strategy_name}: {e}")
    
    async def generate_integrated_signals(self, market_data: Dict[str, Any]) -> List[IntegratedSignal]:
        """Generate integrated trading signals from all active strategies"""
        try:
            logger.debug("🔄 [STRATEGY-INTEGRATOR] Generating integrated signals...")
            
            # Collect signals from all active strategies
            strategy_signals = await self._collect_strategy_signals(market_data)
            
            if not strategy_signals:
                logger.debug("📭 [STRATEGY-INTEGRATOR] No strategy signals collected")
                return []
            
            # Group signals by symbol
            signals_by_symbol = self._group_signals_by_symbol(strategy_signals)
            
            # Integrate signals for each symbol
            integrated_signals = []
            for symbol, signals in signals_by_symbol.items():
                try:
                    integrated_signal = await self._integrate_symbol_signals(symbol, signals, market_data)
                    if integrated_signal:
                        integrated_signals.append(integrated_signal)
                except Exception as e:
                    logger.error(f"❌ [STRATEGY-INTEGRATOR] Error integrating signals for {symbol}: {e}")
                    continue
            
            # Rank and filter signals
            ranked_signals = self._rank_integrated_signals(integrated_signals)
            
            # Apply risk limits
            final_signals = self._apply_risk_limits(ranked_signals)
            
            logger.info(f"✅ [STRATEGY-INTEGRATOR] Generated {len(final_signals)} integrated signals")
            
            # Store signals in history
            for signal in final_signals:
                self.signal_history.append(signal)
            
            return final_signals
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY-INTEGRATOR] Error generating integrated signals: {e}")
            return []
    
    async def _collect_strategy_signals(self, market_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Collect signals from all active strategies"""
        try:
            strategy_signals = []
            
            # Common symbols to analyze
            symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT']
            
            for strategy_name, strategy_info in self.active_strategies.items():
                if not strategy_info['enabled']:
                    continue
                
                try:
                    strategy_instance = strategy_info['instance']
                    strategy_type = strategy_info['type']
                    
                    # Generate signals for each symbol
                    for symbol in symbols:
                        symbol_data = {
                            'symbol': symbol,
                            'current_price': market_data.get('prices', {}).get(symbol, 100.0),
                            'prices': market_data.get('price_history', {}).get(symbol, [100.0] * 20),
                            'volumes': market_data.get('volume_history', {}).get(symbol, [1000.0] * 20),
                            'timestamp': time.time()
                        }
                        
                        # Get signal from strategy
                        signal = await self._get_strategy_signal(strategy_instance, symbol_data)
                        
                        if signal and signal.get('confidence', 0) > 0.5:
                            signal['strategy_name'] = strategy_name
                            signal['strategy_type'] = strategy_type.value
                            signal['symbol'] = symbol
                            strategy_signals.append(signal)
                            
                            # Update strategy tracking
                            strategy_info['last_signal'] = signal
                            strategy_info['signal_count'] += 1
                
                except Exception as e:
                    logger.error(f"❌ [STRATEGY-INTEGRATOR] Error collecting from strategy {strategy_name}: {e}")
                    continue
            
            logger.debug(f"📊 [STRATEGY-INTEGRATOR] Collected {len(strategy_signals)} strategy signals")
            return strategy_signals
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY-INTEGRATOR] Error collecting strategy signals: {e}")
            return []
    
    async def _get_strategy_signal(self, strategy_instance: Any, market_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Get signal from individual strategy"""
        try:
            # Try different method names that strategies might use
            signal_methods = [
                'generate_signal',
                'get_signal',
                'analyze_market',
                'generate_trading_signal',
                'execute_strategy'
            ]
            
            for method_name in signal_methods:
                if hasattr(strategy_instance, method_name):
                    method = getattr(strategy_instance, method_name)
                    
                    # Call the method
                    if asyncio.iscoroutinefunction(method):
                        result = await method(market_data)
                    else:
                        result = method(market_data)
                    
                    # Normalize the result
                    if isinstance(result, dict):
                        return self._normalize_strategy_signal(result)
                    
                    break
            
            # Generate a simple signal if no method found
            return self._generate_fallback_signal(market_data)
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY-INTEGRATOR] Error getting strategy signal: {e}")
            return None
    
    def _normalize_strategy_signal(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize strategy signal to standard format"""
        try:
            normalized = {
                'action': signal.get('action', 'hold').lower(),
                'confidence': float(signal.get('confidence', 0.5)),
                'position_size': float(signal.get('position_size', 0.1)),
                'entry_price': float(signal.get('entry_price', 0.0)),
                'stop_loss': float(signal.get('stop_loss', 0.0)),
                'take_profit': float(signal.get('take_profit', 0.0)),
                'reasoning': signal.get('reasoning', 'Strategy signal'),
                'risk_score': float(signal.get('risk_score', 0.5)),
                'expected_return': float(signal.get('expected_return', 0.0)),
                'holding_period': int(signal.get('holding_period', 60))
            }
            
            # Ensure action is valid
            if normalized['action'] not in ['buy', 'sell', 'hold']:
                normalized['action'] = 'hold'
            
            # Ensure confidence is in valid range
            normalized['confidence'] = max(0.0, min(1.0, normalized['confidence']))
            
            return normalized
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY-INTEGRATOR] Error normalizing signal: {e}")
            return None
    
    def _generate_fallback_signal(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate fallback signal when strategy doesn't provide one"""
        try:
            symbol = market_data.get('symbol', 'UNKNOWN')
            current_price = market_data.get('current_price', 100.0)
            prices = market_data.get('prices', [100.0])
            
            # Simple trend analysis
            if len(prices) >= 5:
                recent_trend = (prices[-1] - prices[-5]) / prices[-5]
                
                if recent_trend > 0.01:  # 1% positive trend
                    action = 'buy'
                    confidence = min(0.7, 0.5 + recent_trend * 10)
                elif recent_trend < -0.01:  # 1% negative trend
                    action = 'sell'
                    confidence = min(0.7, 0.5 + abs(recent_trend) * 10)
                else:
                    action = 'hold'
                    confidence = 0.3
            else:
                action = 'hold'
                confidence = 0.3
            
            return {
                'action': action,
                'confidence': confidence,
                'position_size': 0.05,
                'entry_price': current_price,
                'stop_loss': current_price * (0.98 if action == 'buy' else 1.02),
                'take_profit': current_price * (1.04 if action == 'buy' else 0.96),
                'reasoning': f'Fallback signal based on price trend for {symbol}',
                'risk_score': 0.5,
                'expected_return': 0.02,
                'holding_period': 60
            }
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY-INTEGRATOR] Error generating fallback signal: {e}")
            return None
    
    def _group_signals_by_symbol(self, signals: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """Group signals by symbol"""
        grouped = defaultdict(list)
        
        for signal in signals:
            symbol = signal.get('symbol', 'UNKNOWN')
            grouped[symbol].append(signal)
        
        return dict(grouped)
    
    async def _integrate_symbol_signals(self, symbol: str, signals: List[Dict[str, Any]], 
                                      market_data: Dict[str, Any]) -> Optional[IntegratedSignal]:
        """Integrate multiple signals for a single symbol"""
        try:
            if not signals:
                return None
            
            # Calculate weighted consensus
            consensus_result = self._calculate_weighted_consensus(signals)
            
            if consensus_result['consensus_score'] < self.min_consensus_threshold:
                logger.debug(f"📊 [STRATEGY-INTEGRATOR] Low consensus for {symbol}: {consensus_result['consensus_score']:.3f}")
                return None
            
            # Determine final action
            final_action = consensus_result['action']
            if final_action == 'hold':
                return None
            
            # Calculate position size
            position_size = self._calculate_integrated_position_size(signals, consensus_result)
            
            # Calculate price levels
            entry_price = market_data.get('prices', {}).get(symbol, 100.0)
            stop_loss, take_profit = self._calculate_integrated_levels(signals, entry_price, final_action)
            
            # Calculate risk and return estimates
            risk_score = self._calculate_integrated_risk(signals)
            expected_return = self._calculate_expected_return(signals, final_action)
            
            # Determine holding period
            holding_period = self._calculate_holding_period(signals)
            
            # Generate reasoning
            reasoning = self._generate_integration_reasoning(signals, consensus_result)
            
            # Create integrated signal
            integrated_signal = IntegratedSignal(
                symbol=symbol,
                action=final_action,
                confidence=consensus_result['confidence'],
                strength=self._determine_signal_strength(consensus_result['confidence']),
                position_size=position_size,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                strategy_name='integrated_multi_strategy',
                contributing_strategies=[s['strategy_name'] for s in signals],
                consensus_score=consensus_result['consensus_score'],
                risk_score=risk_score,
                expected_return=expected_return,
                holding_period=holding_period,
                reasoning=reasoning
            )
            
            logger.debug(f"🎯 [STRATEGY-INTEGRATOR] Integrated signal for {symbol}: "
                        f"{final_action} (confidence: {consensus_result['confidence']:.3f})")
            
            return integrated_signal
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY-INTEGRATOR] Error integrating signals for {symbol}: {e}")
            return None
    
    def _calculate_weighted_consensus(self, signals: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate weighted consensus from multiple signals"""
        try:
            if not signals:
                return {'action': 'hold', 'confidence': 0.0, 'consensus_score': 0.0}
            
            # Weight signals by strategy performance and confidence
            weighted_votes = {'buy': 0.0, 'sell': 0.0, 'hold': 0.0}
            total_weight = 0.0
            
            for signal in signals:
                strategy_name = signal.get('strategy_name', 'unknown')
                action = signal.get('action', 'hold')
                confidence = signal.get('confidence', 0.5)
                
                # Get strategy weight
                strategy_performance = self.strategy_performance.get(strategy_name)
                if strategy_performance:
                    strategy_weight = strategy_performance.confidence_score
                else:
                    strategy_weight = 0.5  # Default weight
                
                # Calculate final weight
                final_weight = strategy_weight * confidence
                
                weighted_votes[action] += final_weight
                total_weight += final_weight
            
            # Normalize votes
            if total_weight > 0:
                for action in weighted_votes:
                    weighted_votes[action] /= total_weight
            
            # Determine winning action
            winning_action = max(weighted_votes, key=weighted_votes.get)
            winning_score = weighted_votes[winning_action]
            
            # Calculate consensus score (how much agreement there is)
            entropy = -sum(score * np.log(score + 1e-8) for score in weighted_votes.values())
            max_entropy = np.log(len(weighted_votes))
            consensus_score = 1.0 - (entropy / max_entropy)
            
            return {
                'action': winning_action,
                'confidence': winning_score,
                'consensus_score': consensus_score,
                'vote_distribution': weighted_votes
            }
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY-INTEGRATOR] Error calculating consensus: {e}")
            return {'action': 'hold', 'confidence': 0.0, 'consensus_score': 0.0}
    
    def _calculate_integrated_position_size(self, signals: List[Dict[str, Any]], 
                                          consensus: Dict[str, Any]) -> float:
        """Calculate integrated position size"""
        try:
            # Base position size from consensus confidence
            base_size = consensus['confidence'] * self.max_single_position
            
            # Average suggested position sizes
            suggested_sizes = [s.get('position_size', 0.1) for s in signals]
            avg_suggested_size = np.mean(suggested_sizes)
            
            # Risk adjustment
            risk_scores = [s.get('risk_score', 0.5) for s in signals]
            avg_risk = np.mean(risk_scores)
            risk_adjustment = 1.0 - (avg_risk - 0.5)  # Lower size for higher risk
            
            # Final position size
            position_size = min(base_size, avg_suggested_size) * risk_adjustment
            
            # Apply limits
            position_size = max(0.01, min(self.max_single_position, position_size))
            
            return position_size
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY-INTEGRATOR] Error calculating position size: {e}")
            return 0.05  # Default 5%
    
    def _calculate_integrated_levels(self, signals: List[Dict[str, Any]], 
                                   entry_price: float, action: str) -> Tuple[float, float]:
        """Calculate integrated stop loss and take profit levels"""
        try:
            # Collect stop loss and take profit suggestions
            stop_losses = [s.get('stop_loss', 0.0) for s in signals if s.get('stop_loss', 0.0) > 0]
            take_profits = [s.get('take_profit', 0.0) for s in signals if s.get('take_profit', 0.0) > 0]
            
            if action == 'buy':
                # For buy orders, use most conservative (highest) stop loss
                if stop_losses:
                    stop_loss = max(stop_losses)
                else:
                    stop_loss = entry_price * 0.98  # 2% default
                
                # For buy orders, use most conservative (lowest) take profit
                if take_profits:
                    take_profit = min(take_profits)
                else:
                    take_profit = entry_price * 1.04  # 4% default
            
            else:  # sell
                # For sell orders, use most conservative (lowest) stop loss
                if stop_losses:
                    stop_loss = min(stop_losses)
                else:
                    stop_loss = entry_price * 1.02  # 2% default
                
                # For sell orders, use most conservative (highest) take profit
                if take_profits:
                    take_profit = max(take_profits)
                else:
                    take_profit = entry_price * 0.96  # 4% default
            
            return stop_loss, take_profit
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY-INTEGRATOR] Error calculating levels: {e}")
            if action == 'buy':
                return entry_price * 0.98, entry_price * 1.04
            else:
                return entry_price * 1.02, entry_price * 0.96
    
    def _calculate_integrated_risk(self, signals: List[Dict[str, Any]]) -> float:
        """Calculate integrated risk score"""
        try:
            risk_scores = [s.get('risk_score', 0.5) for s in signals]
            
            # Use maximum risk (most conservative)
            max_risk = max(risk_scores)
            
            # Adjust for signal agreement (more agreement = lower risk)
            actions = [s.get('action', 'hold') for s in signals]
            unique_actions = len(set(actions))
            
            if unique_actions == 1:
                agreement_factor = 0.9  # High agreement
            elif unique_actions == 2:
                agreement_factor = 1.0  # Moderate agreement
            else:
                agreement_factor = 1.2  # Low agreement = higher risk
            
            final_risk = min(1.0, max_risk * agreement_factor)
            
            return final_risk
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY-INTEGRATOR] Error calculating risk: {e}")
            return 0.6  # Default moderate risk
    
    def _calculate_expected_return(self, signals: List[Dict[str, Any]], action: str) -> float:
        """Calculate expected return"""
        try:
            returns = [s.get('expected_return', 0.0) for s in signals 
                      if s.get('action') == action]
            
            if returns:
                # Use conservative estimate (median)
                expected_return = np.median(returns)
            else:
                # Default based on action
                expected_return = 0.02 if action in ['buy', 'sell'] else 0.0
            
            return expected_return
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY-INTEGRATOR] Error calculating expected return: {e}")
            return 0.01
    
    def _calculate_holding_period(self, signals: List[Dict[str, Any]]) -> int:
        """Calculate integrated holding period"""
        try:
            periods = [s.get('holding_period', 60) for s in signals]
            
            # Use median holding period
            holding_period = int(np.median(periods))
            
            return max(5, min(240, holding_period))  # 5 minutes to 4 hours
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY-INTEGRATOR] Error calculating holding period: {e}")
            return 60  # Default 1 hour
    
    def _generate_integration_reasoning(self, signals: List[Dict[str, Any]], 
                                      consensus: Dict[str, Any]) -> str:
        """Generate reasoning for integrated signal"""
        try:
            reasoning_parts = []
            
            # Consensus information
            action = consensus['action']
            confidence = consensus['confidence']
            consensus_score = consensus['consensus_score']
            
            reasoning_parts.append(f"Multi-strategy {action} signal")
            reasoning_parts.append(f"Consensus confidence: {confidence:.3f}")
            reasoning_parts.append(f"Agreement score: {consensus_score:.3f}")
            
            # Contributing strategies
            strategies = [s['strategy_name'] for s in signals]
            reasoning_parts.append(f"Contributing strategies: {', '.join(strategies)}")
            
            # Action distribution
            actions = [s.get('action', 'hold') for s in signals]
            action_counts = {a: actions.count(a) for a in set(actions)}
            reasoning_parts.append(f"Action votes: {action_counts}")
            
            return "; ".join(reasoning_parts)
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY-INTEGRATOR] Error generating reasoning: {e}")
            return "Integrated multi-strategy signal"
    
    def _determine_signal_strength(self, confidence: float) -> SignalStrength:
        """Determine signal strength based on confidence"""
        if confidence >= 0.9:
            return SignalStrength.VERY_STRONG
        elif confidence >= 0.8:
            return SignalStrength.STRONG
        elif confidence >= 0.7:
            return SignalStrength.MODERATE
        elif confidence >= 0.6:
            return SignalStrength.WEAK
        else:
            return SignalStrength.VERY_WEAK
    
    def _rank_integrated_signals(self, signals: List[IntegratedSignal]) -> List[IntegratedSignal]:
        """Rank integrated signals by quality"""
        try:
            if not signals:
                return []
            
            # Calculate ranking score for each signal
            for signal in signals:
                score = (
                    signal.confidence * 0.4 +
                    signal.consensus_score * 0.3 +
                    (1.0 - signal.risk_score) * 0.2 +
                    signal.expected_return * 0.1
                )
                signal.ranking_score = score
            
            # Sort by ranking score (highest first)
            ranked_signals = sorted(signals, key=lambda s: s.ranking_score, reverse=True)
            
            return ranked_signals
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY-INTEGRATOR] Error ranking signals: {e}")
            return signals
    
    def _apply_risk_limits(self, signals: List[IntegratedSignal]) -> List[IntegratedSignal]:
        """Apply risk limits to signals"""
        try:
            if not signals:
                return []
            
            final_signals = []
            total_exposure = 0.0
            
            for signal in signals:
                # Check total exposure limit
                if total_exposure + signal.position_size > self.max_total_exposure:
                    # Reduce position size to fit within limits
                    available_exposure = self.max_total_exposure - total_exposure
                    if available_exposure > 0.01:  # Minimum viable size
                        signal.position_size = available_exposure
                        signal.reasoning += f"; Position size reduced due to exposure limits"
                    else:
                        logger.debug(f"🚫 [STRATEGY-INTEGRATOR] Skipping {signal.symbol} due to exposure limits")
                        continue
                
                # Check risk limits
                if signal.risk_score > 0.8:  # High risk
                    signal.position_size *= 0.5  # Halve position size
                    signal.reasoning += "; Position size reduced due to high risk"
                
                final_signals.append(signal)
                total_exposure += signal.position_size
                
                # Limit number of signals
                if len(final_signals) >= self.max_strategies_per_signal:
                    break
            
            return final_signals
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY-INTEGRATOR] Error applying risk limits: {e}")
            return signals
    
    async def record_signal_outcome(self, signal: IntegratedSignal, execution_result: Dict[str, Any]):
        """Record the outcome of a signal execution"""
        try:
            # Store execution result
            execution_record = {
                'signal': signal,
                'result': execution_result,
                'timestamp': datetime.now(),
                'success': execution_result.get('success', False),
                'profit': execution_result.get('profit', 0.0)
            }
            
            self.execution_history.append(execution_record)
            
            # Update strategy performance
            for strategy_name in signal.contributing_strategies:
                if strategy_name in self.strategy_performance:
                    await self._update_strategy_performance(strategy_name, execution_result)
            
            logger.debug(f"📊 [STRATEGY-INTEGRATOR] Recorded outcome for {signal.symbol}: "
                        f"success={execution_result.get('success', False)}, "
                        f"profit={execution_result.get('profit', 0.0):.4f}")
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY-INTEGRATOR] Error recording signal outcome: {e}")
    
    async def _update_strategy_performance(self, strategy_name: str, execution_result: Dict[str, Any]):
        """Update performance metrics for a strategy"""
        try:
            if strategy_name not in self.strategy_performance:
                return
            
            perf = self.strategy_performance[strategy_name]
            
            # Update trade count
            perf.total_trades += 1
            
            # Update success metrics
            success = execution_result.get('success', False)
            profit = execution_result.get('profit', 0.0)
            
            if success:
                if strategy_name in self.active_strategies:
                    self.active_strategies[strategy_name]['success_count'] += 1
            
            # Update success rate
            if strategy_name in self.active_strategies:
                strategy_info = self.active_strategies[strategy_name]
                perf.success_rate = strategy_info['success_count'] / strategy_info['signal_count']
            
            # Update return metrics
            perf.recent_performance.append(profit)
            if len(perf.recent_performance) > self.performance_window:
                perf.recent_performance.pop(0)
            
            if perf.recent_performance:
                perf.avg_return = np.mean(perf.recent_performance)
                
                # Calculate win rate and profit factor
                wins = [p for p in perf.recent_performance if p > 0]
                losses = [p for p in perf.recent_performance if p < 0]
                
                perf.win_rate = len(wins) / len(perf.recent_performance)
                perf.avg_win = np.mean(wins) if wins else 0.0
                perf.avg_loss = np.mean(losses) if losses else 0.0
                
                if losses and perf.avg_loss != 0:
                    perf.profit_factor = abs(perf.avg_win / perf.avg_loss)
                else:
                    perf.profit_factor = float('inf') if wins else 0.0
            
            # Update confidence score based on recent performance
            if len(perf.recent_performance) >= 10:
                recent_success_rate = len([p for p in perf.recent_performance[-10:] if p > 0]) / 10
                perf.confidence_score = 0.3 + (recent_success_rate * 0.7)  # 0.3 to 1.0 range
            
            perf.last_updated = datetime.now()
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY-INTEGRATOR] Error updating strategy performance: {e}")
    
    async def trigger_strategy_evolution(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Trigger strategy evolution and optimization"""
        try:
            logger.info("🧬 [STRATEGY-INTEGRATOR] Triggering strategy evolution...")
            
            evolution_results = {
                'strategies_evolved': 0,
                'weights_updated': False,
                'performance_improved': False,
                'timestamp': datetime.now()
            }
            
            # Analyze strategy performance
            performance_analysis = self._analyze_strategy_performance()
            
            # Update strategy weights based on performance
            if self._should_update_weights(performance_analysis):
                self._update_strategy_weights(performance_analysis)
                evolution_results['weights_updated'] = True
            
            # Evolve individual strategies if they support it
            for strategy_name, strategy_info in self.active_strategies.items():
                try:
                    strategy_instance = strategy_info['instance']
                    
                    # Check if strategy supports evolution
                    if hasattr(strategy_instance, 'evolve') or hasattr(strategy_instance, 'optimize'):
                        evolution_method = getattr(strategy_instance, 'evolve', None) or getattr(strategy_instance, 'optimize', None)
                        
                        if asyncio.iscoroutinefunction(evolution_method):
                            await evolution_method(market_data, performance_analysis.get(strategy_name, {}))
                        else:
                            evolution_method(market_data, performance_analysis.get(strategy_name, {}))
                        
                        evolution_results['strategies_evolved'] += 1
                        
                except Exception as e:
                    logger.error(f"❌ [STRATEGY-INTEGRATOR] Error evolving strategy {strategy_name}: {e}")
                    continue
            
            logger.info(f"✅ [STRATEGY-INTEGRATOR] Evolution completed: "
                       f"{evolution_results['strategies_evolved']} strategies evolved")
            
            return evolution_results
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY-INTEGRATOR] Error in strategy evolution: {e}")
            return {'error': str(e)}
    
    def _analyze_strategy_performance(self) -> Dict[str, Dict[str, Any]]:
        """Analyze performance of all strategies"""
        try:
            analysis = {}
            
            for strategy_name, perf in self.strategy_performance.items():
                analysis[strategy_name] = {
                    'success_rate': perf.success_rate,
                    'avg_return': perf.avg_return,
                    'win_rate': perf.win_rate,
                    'profit_factor': perf.profit_factor,
                    'confidence_score': perf.confidence_score,
                    'total_trades': perf.total_trades,
                    'recent_performance': perf.recent_performance[-10:],  # Last 10 trades
                    'trend': 'improving' if len(perf.recent_performance) >= 10 and 
                            np.mean(perf.recent_performance[-5:]) > np.mean(perf.recent_performance[-10:-5]) else 'declining'
                }
            
            return analysis
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY-INTEGRATOR] Error analyzing performance: {e}")
            return {}
    
    def _should_update_weights(self, performance_analysis: Dict[str, Dict[str, Any]]) -> bool:
        """Determine if strategy weights should be updated"""
        try:
            # Update weights every 20 evolution cycles or if significant performance divergence
            evolution_cycle = len(self.execution_history) // 20
            
            if evolution_cycle % 5 == 0:  # Every 5th cycle
                return True
            
            # Check for significant performance divergence
            if len(performance_analysis) >= 2:
                success_rates = [data['success_rate'] for data in performance_analysis.values()]
                performance_spread = max(success_rates) - min(success_rates)
                
                if performance_spread > 0.3:  # 30% spread
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY-INTEGRATOR] Error checking weight update: {e}")
            return False
    
    def _update_strategy_weights(self, performance_analysis: Dict[str, Dict[str, Any]]):
        """Update strategy weights based on performance"""
        try:
            if not performance_analysis:
                return
            
            # Calculate new weights based on performance
            new_weights = {}
            total_score = 0.0
            
            for strategy_name, analysis in performance_analysis.items():
                # Calculate performance score
                score = (
                    analysis['success_rate'] * 0.4 +
                    analysis['win_rate'] * 0.3 +
                    min(1.0, analysis['profit_factor'] / 2.0) * 0.2 +
                    analysis['confidence_score'] * 0.1
                )
                
                new_weights[strategy_name] = score
                total_score += score
            
            # Normalize weights
            if total_score > 0:
                for strategy_name in new_weights:
                    new_weights[strategy_name] /= total_score
                
                # Apply adaptation rate to smooth weight changes
                for strategy_name in new_weights:
                    if strategy_name in self.strategy_weights:
                        old_weight = self.strategy_weights[strategy_name]
                        new_weight = new_weights[strategy_name]
                        
                        # Smooth transition
                        self.strategy_weights[strategy_name] = (
                            old_weight * (1 - self.adaptation_rate) +
                            new_weight * self.adaptation_rate
                        )
                    else:
                        self.strategy_weights[strategy_name] = new_weights[strategy_name]
            
            logger.info(f"📊 [STRATEGY-INTEGRATOR] Updated strategy weights based on performance")
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY-INTEGRATOR] Error updating strategy weights: {e}")
    
    def get_integration_status(self) -> Dict[str, Any]:
        """Get current integration status and metrics"""
        try:
            return {
                'active_strategies': len(self.active_strategies),
                'strategy_names': list(self.active_strategies.keys()),
                'strategy_weights': dict(self.strategy_weights),
                'total_signals_generated': len(self.signal_history),
                'total_executions': len(self.execution_history),
                'recent_performance': {
                    name: {
                        'success_rate': perf.success_rate,
                        'avg_return': perf.avg_return,
                        'confidence_score': perf.confidence_score
                    }
                    for name, perf in self.strategy_performance.items()
                },
                'risk_limits': {
                    'max_total_exposure': self.max_total_exposure,
                    'max_single_position': self.max_single_position,
                    'min_consensus_threshold': self.min_consensus_threshold
                }
            }
            
        except Exception as e:
            logger.error(f"❌ [STRATEGY-INTEGRATOR] Error getting status: {e}")
            return {'error': str(e)}

# Export the main class
__all__ = ['ComprehensiveStrategyIntegrator', 'IntegratedSignal', 'StrategyPerformance', 'StrategyType', 'SignalStrength']
