#!/usr/bin/env python3
"""
Test script to verify all missing components have been implemented
"""

def test_neural_strategy_manager():
    """Test neural strategy manager import"""
    try:
        from src.strategies.neural_strategy_manager import get_neural_strategy_manager
        print("✅ Neural strategy manager import successful")
        
        # Test instantiation
        manager = get_neural_strategy_manager()
        print(f"✅ Neural strategy manager created: {type(manager).__name__}")
        
        # Test that manager has required methods
        if hasattr(manager, 'get_trading_signal'):
            print("✅ Neural strategy manager has get_trading_signal method")
        else:
            print("❌ Neural strategy manager missing get_trading_signal method")
            return False
            
        return True
    except Exception as e:
        import traceback
        print(f"❌ Neural strategy manager failed: {e}")
        print("Full error:")
        traceback.print_exc()
        return False

def test_market_context():
    """Test MarketContext import"""
    try:
        from src.strategies.base import MarketContext
        print("✅ MarketContext import successful")
        
        # Test instantiation
        context = MarketContext(
            price_data={},
            order_books={},
            sentiment_data={},
            technical_indicators={},
            volume_data={},
            news_data=[],
            arbitrage_opportunities=[],
            portfolio_state={},
            risk_metrics={},
            timestamp=None
        )
        print(f"✅ MarketContext created: {type(context).__name__}")
        return True
    except Exception as e:
        print(f"❌ MarketContext failed: {e}")
        return False

def test_signal_data():
    """Test SignalData import"""
    try:
        from src.strategies.base import SignalData
        print("✅ SignalData import successful")
        
        # Test instantiation
        signal = SignalData(
            action="BUY",
            symbol="BTC-USD",
            confidence=0.8
        )
        print(f"✅ SignalData created: {type(signal).__name__}")
        return True
    except Exception as e:
        print(f"❌ SignalData failed: {e}")
        return False

def test_hft_momentum_strategy():
    """Test HFTMomentumStrategy import"""
    try:
        from src.strategies.hft_momentum import HFTMomentumStrategy
        print("✅ HFTMomentumStrategy import successful")
        
        # Test instantiation
        strategy = HFTMomentumStrategy()
        print(f"✅ HFTMomentumStrategy created: {type(strategy).__name__}")
        return True
    except Exception as e:
        print(f"❌ HFTMomentumStrategy failed: {e}")
        return False

def test_main_imports():
    """Test main.py imports"""
    try:
        import sys
        sys.path.append('.')
        
        # Test the specific imports that were failing in main.py
        from src.strategies.neural_strategy_manager import get_neural_strategy_manager
        from src.strategies.base import MarketContext, SignalData
        from src.strategies.hft_momentum import HFTMomentumStrategy
        
        print("✅ All main.py imports successful")
        return True
    except Exception as e:
        print(f"❌ Main imports failed: {e}")
        return False

def main():
    """Run all import tests"""
    print("🧪 Testing AutoGPT Trading System Import Fixes")
    print("=" * 50)
    
    tests = [
        test_neural_strategy_manager,
        test_market_context,
        test_signal_data,
        test_hft_momentum_strategy,
        test_main_imports
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL IMPORT FIXES SUCCESSFUL! System ready for trading.")
        return True
    else:
        print("⚠️  Some imports still need fixing.")
        return False

if __name__ == "__main__":
    main()
