#!/usr/bin/env python3
"""Fix ArbitrageOpportunity instances to include required_margin"""

import re

def fix_arbitrage_opportunities():
    file_path = 'src/trading/futures_basis_trading_engine.py'
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find all ArbitrageOpportunity instantiations and add required_margin
    patterns_to_fix = [
        # Pattern 1: risk_score= followed by closing parenthesis
        (r'(risk_score=.*?)(\s*\))', r'\1,\n                    required_margin=current_price / (optimal_leverage if "optimal_leverage" in locals() else leverage)\2'),
        
        # Pattern 2: risk_score= followed by comma (in case there are other parameters after)
        (r'(risk_score=.*?)(,\s*[^r])', r'\1,\n                    required_margin=current_price / (optimal_leverage if "optimal_leverage" in locals() else leverage)\2'),
    ]
    
    updated_content = content
    
    for pattern, replacement in patterns_to_fix:
        updated_content = re.sub(pattern, replacement, updated_content)
    
    # Special handling for lines that already have required_margin to avoid duplicates
    lines = updated_content.split('\n')
    cleaned_lines = []
    
    i = 0
    while i < len(lines):
        line = lines[i]
        
        # Skip duplicate required_margin lines
        if 'required_margin=' in line and i > 0:
            prev_line = lines[i-1]
            if 'required_margin=' in prev_line:
                i += 1
                continue
        
        cleaned_lines.append(line)
        i += 1
    
    final_content = '\n'.join(cleaned_lines)
    
    # Write back to file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(final_content)
    
    print('✅ Fixed all ArbitrageOpportunity instances with required_margin')

if __name__ == '__main__':
    fix_arbitrage_opportunities()
