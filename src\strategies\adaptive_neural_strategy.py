"""
Adaptive Neural Strategy System
Advanced, neural-network controlled trading strategies that adapt based on live learning
"""

import asyncio
import numpy as np
import logging
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
import json
from collections import deque
from enum import Enum

from .base import BaseStrategy, SignalData, MarketContext

logger = logging.getLogger(__name__)

class MarketRegime(Enum):
    """Market regime classification"""
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    SIDEWAYS = "sideways"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"
    BREAKOUT = "breakout"
    REVERSAL = "reversal"

@dataclass
class StrategyParameters:
    """Dynamic strategy parameters that can be adjusted by neural networks"""
    momentum_threshold: float = 0.02
    volatility_threshold: float = 0.05
    volume_threshold: float = 1.5
    rsi_oversold: float = 30.0
    rsi_overbought: float = 70.0
    macd_signal_threshold: float = 0.001
    bollinger_std: float = 2.0
    stop_loss_pct: float = 0.02
    take_profit_pct: float = 0.05
    position_size_pct: float = 0.1
    confidence_threshold: float = 0.45  # OPTIMIZED: Reduced from 0.6 to 0.45 for more signals
    risk_multiplier: float = 1.0
    adaptation_rate: float = 0.15  # OPTIMIZED: Increased from 0.1 to 0.15 for faster adaptation
    
    def to_dict(self) -> Dict[str, float]:
        """Convert to dictionary for neural network input"""
        return asdict(self)
    
    def from_dict(self, params: Dict[str, float]):
        """Update from dictionary (neural network output)"""
        for key, value in params.items():
            if hasattr(self, key):
                setattr(self, key, float(value))

@dataclass
class MarketFeatures:
    """Comprehensive market feature set for neural analysis"""
    price: float
    volume: float
    volatility: float
    rsi: float
    macd: float
    macd_signal: float
    bollinger_upper: float
    bollinger_lower: float
    bollinger_position: float
    momentum_1m: float
    momentum_5m: float
    momentum_15m: float
    volume_ratio: float
    price_change_1h: float
    price_change_4h: float
    price_change_24h: float
    support_level: float
    resistance_level: float
    trend_strength: float
    market_sentiment: float
    news_impact: float
    cross_exchange_spread: float
    liquidity_score: float
    order_book_imbalance: float
    
    def to_array(self) -> np.ndarray:
        """Convert to numpy array for neural network input"""
        return np.array([
            self.price, self.volume, self.volatility, self.rsi, self.macd,
            self.macd_signal, self.bollinger_upper, self.bollinger_lower,
            self.bollinger_position, self.momentum_1m, self.momentum_5m,
            self.momentum_15m, self.volume_ratio, self.price_change_1h,
            self.price_change_4h, self.price_change_24h, self.support_level,
            self.resistance_level, self.trend_strength, self.market_sentiment,
            self.news_impact, self.cross_exchange_spread, self.liquidity_score,
            self.order_book_imbalance
        ], dtype=np.float32)

class AdaptiveNeuralStrategy(BaseStrategy):
    """
    Advanced neural-network controlled strategy that adapts based on live learning
    Combines multiple timeframes, market regimes, and real-time adaptation
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(config or {})
        
        # Strategy parameters (neural-network adjustable)
        self.params = StrategyParameters()
        
        # Market regime detection
        self.current_regime = MarketRegime.SIDEWAYS
        self.regime_confidence = 0.5
        self.regime_history = deque(maxlen=100)
        
        # Multi-timeframe analysis
        self.timeframes = ['1m', '5m', '15m', '1h', '4h']
        self.market_data_cache = {tf: deque(maxlen=200) for tf in self.timeframes}
        
        # Neural network integration
        self.neural_enabled = False
        self.neural_agent = None
        self.strategy_optimizer = None
        
        # Performance tracking for adaptation
        self.performance_history = deque(maxlen=1000)
        self.adaptation_triggers = {
            'poor_performance': 0.4,  # Win rate below 40%
            'high_drawdown': 0.1,     # Drawdown above 10%
            'regime_change': True,    # Market regime changes
            'volatility_spike': 2.0   # Volatility 2x normal
        }
        
        # Advanced indicators
        self.indicators = {}
        self.support_resistance_levels = {'support': [], 'resistance': []}
        
        # Real-time learning components
        self.learning_enabled = True
        self.last_adaptation = datetime.now()
        self.adaptation_interval = timedelta(hours=1)

        # CRITICAL FIX: Add missing _should_adapt attribute
        self._should_adapt_flag = False
        self._adaptation_counter = 0
        self._last_performance_check = datetime.now()

        self._init_neural_components()

        logger.info("AdaptiveNeuralStrategy initialized with neural adaptation")
    
    def _init_neural_components(self):
        """Initialize neural network components"""
        try:
            # For now, disable neural components to avoid hanging
            # TODO: Fix neural component initialization
            logger.info("Neural components temporarily disabled for stability")
            self.neural_enabled = False
            self.neural_agent = None
            self.strategy_optimizer = None

            # # Try to import neural components
            # from ..neural.hybrid_agent import HybridTradingAgent
            # from ..neural.reinforcement_learning import ReinforcementLearningAgent
            #
            # # Initialize neural agent for strategy optimization
            # neural_config = {
            #     'state_dim': 24,  # Market features
            #     'action_dim': 13,  # Strategy parameters to adjust
            #     'learning_rate': 0.001,
            #     'memory_size': 10000
            # }
            #
            # self.neural_agent = HybridTradingAgent(neural_config)
            # self.strategy_optimizer = ReinforcementLearningAgent(
            #     state_size=24,
            #     action_size=13,
            #     learning_rate=0.0005
            # )
            #
            # self.neural_enabled = True
            # logger.info("Neural components initialized for strategy adaptation")

        except Exception as e:
            logger.warning(f"Neural components not available - using static strategy: {e}")
            self.neural_enabled = False
    
    async def analyze_market(self, market_context: MarketContext) -> Optional[SignalData]:
        """
        Advanced market analysis with neural adaptation
        """
        try:
            # Extract comprehensive market features
            features = await self._extract_market_features(market_context)
            
            # Detect market regime
            await self._detect_market_regime(features, market_context)
            
            # Adapt strategy parameters if needed
            if self.neural_enabled and self._should_adapt():
                await self._adapt_strategy_parameters(features)
            
            # Generate signals based on current regime and parameters
            signal = await self._generate_adaptive_signal(features, market_context)
            
            # Apply neural enhancement if available
            if self.neural_enabled and signal:
                signal = await self._enhance_signal_with_neural(signal, features)
            
            # Record performance for future adaptation
            if signal:
                await self._record_signal_for_learning(signal, features)
            
            return signal
            
        except Exception as e:
            logger.error(f"Error in adaptive market analysis: {e}")
            return None
    
    async def _extract_market_features(self, market_context: MarketContext) -> MarketFeatures:
        """Extract comprehensive market features for neural analysis"""
        try:
            # CRITICAL FIX: Handle both attribute and dictionary access patterns for market_context
            data = {}
            price_data = None

            # Handle different market_context data structures
            if hasattr(market_context, 'price_data'):
                price_data = market_context.price_data
            elif isinstance(market_context, dict) and 'price_data' in market_context:
                price_data = market_context['price_data']
            else:
                logger.error(f"Invalid market_context structure: {type(market_context)}")
                if isinstance(market_context, dict):
                    logger.info(f"Available keys: {list(market_context.keys())}")
                price_data = {}

            # Extract data from price_data structure
            if price_data and isinstance(price_data, dict):
                first_exchange = list(price_data.keys())[0] if price_data else None
                if first_exchange and price_data[first_exchange]:
                    first_symbol = list(price_data[first_exchange].keys())[0]
                    data = price_data[first_exchange][first_symbol]
            
            # Basic price and volume
            price = float(data.get('price', 0))
            volume = float(data.get('volume', 0))

            # CRITICAL: NO MOCK DATA - USE REAL HISTORICAL DATA ONLY
            historical_data = data.get('history', [])
            if not historical_data:
                # CRITICAL: No fallback to synthetic data - log error and use minimal real data
                logger.error(f"[CRITICAL] No real historical data available - cannot generate synthetic fallback")
                logger.error(f"[CRITICAL] Strategy will use current price only - reduced accuracy expected")
                # Use only current real price point - no synthetic historical data
                historical_data = [{'price': price, 'volume': volume}] if price > 0 else []

            # Technical indicators
            rsi = self._calculate_rsi(historical_data)
            macd, macd_signal = self._calculate_macd(historical_data)
            bb_upper, bb_lower = self._calculate_bollinger_bands(historical_data)

            # Multi-timeframe momentum
            momentum_1m = self._calculate_momentum(historical_data, 1)
            momentum_5m = self._calculate_momentum(historical_data, 5)
            momentum_15m = self._calculate_momentum(historical_data, 15)

            # Volume analysis
            avg_volume = np.mean([d.get('volume', 0) for d in historical_data[-20:]])
            volume_ratio = volume / max(avg_volume, 1)

            # Price changes (with safe division)
            prices = [float(d.get('price', 0)) for d in historical_data]
            price_change_1h = (price - prices[-60]) / max(prices[-60], 0.001) if len(prices) >= 60 and prices[-60] > 0 else 0
            price_change_4h = (price - prices[-240]) / max(prices[-240], 0.001) if len(prices) >= 240 and prices[-240] > 0 else 0
            price_change_24h = (price - prices[-1440]) / max(prices[-1440], 0.001) if len(prices) >= 1440 and prices[-1440] > 0 else 0

            # Support and resistance
            support_level, resistance_level = self._calculate_support_resistance(prices)
            
            # Volatility (with safe division)
            if len(prices) >= 20:
                mean_price = np.mean(prices[-20:])
                volatility = np.std(prices[-20:]) / max(mean_price, 0.001) if mean_price > 0 else 0
            else:
                volatility = 0
            
            # Market sentiment (from internet crawler if available) - CRITICAL FIX
            sentiment_data = {}
            if hasattr(market_context, 'sentiment_data'):
                sentiment_data = market_context.sentiment_data or {}
            elif isinstance(market_context, dict) and 'sentiment_data' in market_context:
                sentiment_data = market_context['sentiment_data'] or {}

            market_sentiment = sentiment_data.get('overall', 0.0)
            news_impact = sentiment_data.get('high_impact_count', 0) / 10.0
            
            # Cross-exchange data - CRITICAL FIX
            arbitrage_opportunities = []
            if hasattr(market_context, 'arbitrage_opportunities'):
                arbitrage_opportunities = market_context.arbitrage_opportunities or []
            elif isinstance(market_context, dict) and 'arbitrage_opportunities' in market_context:
                arbitrage_opportunities = market_context['arbitrage_opportunities'] or []

            cross_exchange_spread = arbitrage_opportunities[0].get('spread_percent', 0.0) if arbitrage_opportunities else 0.0

            # Order book analysis - CRITICAL FIX
            order_book = {}
            order_books = None
            if hasattr(market_context, 'order_books'):
                order_books = market_context.order_books
            elif isinstance(market_context, dict) and 'order_books' in market_context:
                order_books = market_context['order_books']

            if order_books and isinstance(order_books, dict):
                first_exchange = list(order_books.keys())[0] if order_books else None
                if first_exchange and order_books[first_exchange]:
                    first_symbol = list(order_books[first_exchange].keys())[0]
                    order_book = order_books[first_exchange][first_symbol]
            liquidity_score = self._calculate_liquidity_score(order_book)
            order_book_imbalance = self._calculate_order_book_imbalance(order_book)
            
            # Trend strength
            trend_strength = self._calculate_trend_strength(prices)
            
            return MarketFeatures(
                price=price,
                volume=volume,
                volatility=volatility,
                rsi=rsi,
                macd=macd,
                macd_signal=macd_signal,
                bollinger_upper=bb_upper,
                bollinger_lower=bb_lower,
                bollinger_position=(price - bb_lower) / max(bb_upper - bb_lower, 0.001),
                momentum_1m=momentum_1m,
                momentum_5m=momentum_5m,
                momentum_15m=momentum_15m,
                volume_ratio=volume_ratio,
                price_change_1h=price_change_1h,
                price_change_4h=price_change_4h,
                price_change_24h=price_change_24h,
                support_level=support_level,
                resistance_level=resistance_level,
                trend_strength=trend_strength,
                market_sentiment=market_sentiment,
                news_impact=news_impact,
                cross_exchange_spread=cross_exchange_spread,
                liquidity_score=liquidity_score,
                order_book_imbalance=order_book_imbalance
            )
            
        except Exception as e:
            logger.error(f"Error extracting market features: {e}")
            # Return default features
            return MarketFeatures(
                price=0, volume=0, volatility=0, rsi=50, macd=0, macd_signal=0,
                bollinger_upper=0, bollinger_lower=0, bollinger_position=0.5,
                momentum_1m=0, momentum_5m=0, momentum_15m=0, volume_ratio=1,
                price_change_1h=0, price_change_4h=0, price_change_24h=0,
                support_level=0, resistance_level=0, trend_strength=0,
                market_sentiment=0, news_impact=0, cross_exchange_spread=0,
                liquidity_score=0.5, order_book_imbalance=0
            )

    async def _detect_market_regime(self, features: MarketFeatures, market_context: MarketContext):
        """Detect current market regime using advanced analysis"""
        try:
            # Multi-factor regime detection
            volatility_factor = features.volatility
            trend_factor = features.trend_strength
            momentum_factor = (features.momentum_1m + features.momentum_5m + features.momentum_15m) / 3

            # Regime classification logic
            if volatility_factor > self.params.volatility_threshold * 2:
                new_regime = MarketRegime.HIGH_VOLATILITY
                confidence = min(volatility_factor / max(self.params.volatility_threshold * 2, 0.001), 1.0)
            elif volatility_factor < self.params.volatility_threshold * 0.5:
                new_regime = MarketRegime.LOW_VOLATILITY
                confidence = 1.0 - (volatility_factor / max(self.params.volatility_threshold * 0.5, 0.001))
            elif abs(momentum_factor) > self.params.momentum_threshold:
                if momentum_factor > 0:
                    new_regime = MarketRegime.TRENDING_UP
                else:
                    new_regime = MarketRegime.TRENDING_DOWN
                confidence = min(abs(momentum_factor) / max(self.params.momentum_threshold, 0.001), 1.0)
            elif abs(trend_factor) < 0.3:
                new_regime = MarketRegime.SIDEWAYS
                confidence = 1.0 - abs(trend_factor) / max(0.3, 0.001)
            else:
                # Check for breakout or reversal patterns
                if self._detect_breakout_pattern(features):
                    new_regime = MarketRegime.BREAKOUT
                    confidence = 0.7
                elif self._detect_reversal_pattern(features):
                    new_regime = MarketRegime.REVERSAL
                    confidence = 0.6
                else:
                    new_regime = MarketRegime.SIDEWAYS
                    confidence = 0.5

            # Update regime if confidence is high enough
            if confidence > 0.6:
                if new_regime != self.current_regime:
                    logger.info(f"Market regime changed: {self.current_regime.value} -> {new_regime.value} "
                               f"(confidence: {confidence:.2f})")
                    self.current_regime = new_regime
                    self.regime_confidence = confidence

            # Store regime history
            self.regime_history.append({
                'regime': new_regime,
                'confidence': confidence,
                'timestamp': datetime.now(),
                'features': features.to_array()
            })

        except Exception as e:
            logger.error(f"Error detecting market regime: {e}")

    async def _generate_adaptive_signal(self, features: MarketFeatures, market_context: MarketContext) -> Optional[SignalData]:
        """Generate trading signal based on current regime and adaptive parameters"""
        try:
            signal_strength = 0.0
            signal_direction = 0  # -1 = sell, 0 = hold, 1 = buy
            reasoning = []

            # Regime-specific signal generation
            if self.current_regime == MarketRegime.TRENDING_UP:
                signal_strength, signal_direction = self._generate_trend_following_signal(features, 1)
                reasoning.append(f"Trending up regime detected")
            elif self.current_regime == MarketRegime.TRENDING_DOWN:
                signal_strength, signal_direction = self._generate_trend_following_signal(features, -1)
                reasoning.append(f"Trending down regime detected")
            elif self.current_regime == MarketRegime.SIDEWAYS:
                signal_strength, signal_direction = self._generate_mean_reversion_signal(features)
                reasoning.append(f"Sideways regime - mean reversion strategy")
            elif self.current_regime == MarketRegime.HIGH_VOLATILITY:
                signal_strength, signal_direction = self._generate_volatility_signal(features)
                reasoning.append(f"High volatility regime")
            elif self.current_regime == MarketRegime.BREAKOUT:
                signal_strength, signal_direction = self._generate_breakout_signal(features)
                reasoning.append(f"Breakout pattern detected")
            elif self.current_regime == MarketRegime.REVERSAL:
                signal_strength, signal_direction = self._generate_reversal_signal(features)
                reasoning.append(f"Reversal pattern detected")

            # Apply confidence threshold
            if abs(signal_strength) < self.params.confidence_threshold:
                return None

            # Create signal
            if signal_direction != 0:
                action = "BUY" if signal_direction > 0 else "SELL"

                # Calculate position size based on regime and volatility
                position_size = self._calculate_adaptive_position_size(features, signal_strength)

                # Calculate stop loss and take profit
                stop_loss, take_profit = self._calculate_adaptive_levels(features, signal_direction)

                # Get symbol and exchange from market context
                symbol = "BTC-USD"  # Default symbol
                exchange = "coinbase"  # Default exchange

                # CRITICAL FIX: Handle market context data structures safely
                exchange = 'bybit'  # Default exchange
                symbol = 'BTCUSDT'  # Default symbol

                try:
                    if hasattr(market_context, 'price_data') and market_context.price_data:
                        if isinstance(market_context.price_data, dict) and market_context.price_data:
                            exchange = list(market_context.price_data.keys())[0]
                            if exchange in market_context.price_data and market_context.price_data[exchange]:
                                symbol = list(market_context.price_data[exchange].keys())[0]
                except Exception as data_error:
                    logger.warning(f"Error accessing market context data structure: {data_error}")
                    # Use defaults: exchange='bybit', symbol='BTCUSDT'

                signal = SignalData(
                    symbol=symbol,
                    exchange=exchange,
                    action=action,
                    amount=Decimal(str(position_size)),
                    confidence=abs(signal_strength),
                    reasoning=" | ".join(reasoning),
                    timestamp=datetime.now(),
                    price=Decimal(str(features.price)),
                    stop_loss=Decimal(str(stop_loss * features.price)),
                    take_profit=Decimal(str(take_profit * features.price))
                )

                return signal

            return None

        except Exception as e:
            logger.error(f"Error generating adaptive signal: {e}")
            return None

    def _generate_trend_following_signal(self, features: MarketFeatures, trend_direction: int) -> Tuple[float, int]:
        """Generate trend following signal"""
        signal_strength = 0.0
        signal_direction = 0

        # Momentum confirmation
        momentum_score = (features.momentum_1m + features.momentum_5m + features.momentum_15m) / 3
        if trend_direction * momentum_score > self.params.momentum_threshold:
            signal_strength += 0.3

        # Volume confirmation
        if features.volume_ratio > self.params.volume_threshold:
            signal_strength += 0.2

        # MACD confirmation
        if trend_direction * (features.macd - features.macd_signal) > self.params.macd_signal_threshold:
            signal_strength += 0.2

        # Bollinger band position
        if trend_direction > 0 and features.bollinger_position > 0.3:
            signal_strength += 0.15
        elif trend_direction < 0 and features.bollinger_position < 0.7:
            signal_strength += 0.15

        # News sentiment
        if trend_direction * features.market_sentiment > 0.1:
            signal_strength += 0.15

        signal_direction = trend_direction if signal_strength > self.params.confidence_threshold else 0

        return signal_strength, signal_direction

    def _generate_mean_reversion_signal(self, features: MarketFeatures) -> Tuple[float, int]:
        """Generate mean reversion signal for sideways markets"""
        signal_strength = 0.0
        signal_direction = 0

        # RSI extremes
        if features.rsi < self.params.rsi_oversold:
            signal_strength += 0.4
            signal_direction = 1
        elif features.rsi > self.params.rsi_overbought:
            signal_strength += 0.4
            signal_direction = -1

        # Bollinger band extremes
        if features.bollinger_position < 0.1:
            signal_strength += 0.3
            signal_direction = 1
        elif features.bollinger_position > 0.9:
            signal_strength += 0.3
            signal_direction = -1

        # Support/resistance levels
        if features.price <= features.support_level * 1.005:
            signal_strength += 0.3
            signal_direction = 1
        elif features.price >= features.resistance_level * 0.995:
            signal_strength += 0.3
            signal_direction = -1

        return signal_strength, signal_direction

    def _detect_breakout_pattern(self, features: MarketFeatures) -> bool:
        """Detect breakout patterns"""
        try:
            volume_breakout = features.volume_ratio > 2.0
            price_breakout = (
                features.price > features.resistance_level * 1.01 or
                features.price < features.support_level * 0.99
            )
            bb_breakout = features.bollinger_position > 0.95 or features.bollinger_position < 0.05
            return volume_breakout and (price_breakout or bb_breakout)
        except Exception:
            return False

    def _detect_reversal_pattern(self, features: MarketFeatures) -> bool:
        """Detect reversal patterns"""
        try:
            rsi_extreme = features.rsi < 30 or features.rsi > 70
            macd_reversal = (
                features.macd > features.macd_signal and features.macd < 0 or
                features.macd < features.macd_signal and features.macd > 0
            )
            momentum_divergence = features.momentum_1m * features.momentum_15m < 0
            return rsi_extreme and (macd_reversal or momentum_divergence)
        except Exception:
            return False

    def _generate_volatility_signal(self, features: MarketFeatures) -> Tuple[float, int]:
        """Generate signal for high volatility regime"""
        signal_strength = 0.0
        signal_direction = 0

        # Reduce position size in high volatility
        if features.volatility > self.params.volatility_threshold * 1.5:
            # Look for strong momentum with volume confirmation
            momentum = (features.momentum_1m + features.momentum_5m) / 2
            if abs(momentum) > self.params.momentum_threshold * 1.5 and features.volume_ratio > 2.0:
                signal_strength = 0.6
                signal_direction = 1 if momentum > 0 else -1

        return signal_strength, signal_direction

    def _generate_breakout_signal(self, features: MarketFeatures) -> Tuple[float, int]:
        """Generate breakout signal"""
        signal_strength = 0.0
        signal_direction = 0

        # Volume confirmation is critical for breakouts
        if features.volume_ratio > 2.0:
            if features.price > features.resistance_level * 1.01:
                signal_strength = 0.8
                signal_direction = 1
            elif features.price < features.support_level * 0.99:
                signal_strength = 0.8
                signal_direction = -1

        return signal_strength, signal_direction

    def _generate_reversal_signal(self, features: MarketFeatures) -> Tuple[float, int]:
        """Generate reversal signal"""
        signal_strength = 0.0
        signal_direction = 0

        # RSI reversal
        if features.rsi < 25:
            signal_strength = 0.7
            signal_direction = 1
        elif features.rsi > 75:
            signal_strength = 0.7
            signal_direction = -1

        # MACD confirmation
        if signal_direction != 0:
            if signal_direction * (features.macd - features.macd_signal) > 0:
                signal_strength += 0.2

        return signal_strength, signal_direction

    def _calculate_adaptive_position_size(self, features: MarketFeatures, signal_strength: float) -> float:
        """Calculate position size based on market conditions"""
        base_size = self.params.position_size_pct

        # Adjust for volatility
        volatility_adjustment = 1.0 / (1.0 + features.volatility * 10)

        # Adjust for signal strength
        confidence_adjustment = signal_strength

        # Adjust for regime
        regime_adjustment = 1.0
        if self.current_regime == MarketRegime.HIGH_VOLATILITY:
            regime_adjustment = 0.5
        elif self.current_regime == MarketRegime.BREAKOUT:
            regime_adjustment = 1.2

        final_size = base_size * volatility_adjustment * confidence_adjustment * regime_adjustment
        return min(final_size, 0.25)  # Cap at 25%

    def _calculate_adaptive_levels(self, features: MarketFeatures, direction: int) -> Tuple[float, float]:
        """Calculate adaptive stop loss and take profit levels"""
        # Base levels
        stop_loss = self.params.stop_loss_pct
        take_profit = self.params.take_profit_pct

        # Adjust for volatility
        volatility_multiplier = 1.0 + features.volatility * 5
        stop_loss *= volatility_multiplier
        take_profit *= volatility_multiplier

        # Adjust for regime
        if self.current_regime == MarketRegime.HIGH_VOLATILITY:
            stop_loss *= 1.5
            take_profit *= 1.5
        elif self.current_regime == MarketRegime.BREAKOUT:
            take_profit *= 2.0  # Let winners run on breakouts

        return stop_loss, take_profit

    # Technical indicator calculations
    def _calculate_rsi(self, historical_data: List[Dict], period: int = 14) -> float:
        """Calculate RSI"""
        try:
            prices = [float(d.get('price', 0)) for d in historical_data[-period-1:]]
            if len(prices) < period + 1:
                return 50.0

            deltas = np.diff(prices)
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)

            avg_gain = np.mean(gains)
            avg_loss = np.mean(losses)

            if avg_loss == 0:
                return 100.0

            rs = avg_gain / max(avg_loss, 0.001)  # Safe division
            rsi = 100 - (100 / (1 + rs))
            return float(rsi)
        except:
            return 50.0

    def _calculate_macd(self, historical_data: List[Dict]) -> Tuple[float, float]:
        """Calculate MACD and signal line"""
        try:
            prices = [float(d.get('price', 0)) for d in historical_data[-50:]]
            if len(prices) < 26:
                return 0.0, 0.0

            ema12 = self._calculate_ema(prices, 12)
            ema26 = self._calculate_ema(prices, 26)
            macd = ema12 - ema26

            # Simple signal line (could be enhanced with EMA)
            signal = macd * 0.9  # Simplified

            return float(macd), float(signal)
        except:
            return 0.0, 0.0

    def _calculate_ema(self, prices: List[float], period: int) -> float:
        """Calculate Exponential Moving Average"""
        try:
            if len(prices) < period:
                return np.mean(prices)

            multiplier = 2 / max(period + 1, 1)  # Safe division
            ema = prices[0]

            for price in prices[1:]:
                ema = (price * multiplier) + (ema * (1 - multiplier))

            return ema
        except:
            return 0.0

    def _calculate_bollinger_bands(self, historical_data: List[Dict], period: int = 20) -> Tuple[float, float]:
        """Calculate Bollinger Bands"""
        try:
            prices = [float(d.get('price', 0)) for d in historical_data[-period:]]
            if len(prices) < period:
                return 0.0, 0.0

            sma = np.mean(prices)
            std = np.std(prices)

            upper = sma + (std * self.params.bollinger_std)
            lower = sma - (std * self.params.bollinger_std)

            return float(upper), float(lower)
        except:
            return 0.0, 0.0

    def _calculate_momentum(self, historical_data: List[Dict], periods: int) -> float:
        """Calculate price momentum"""
        try:
            prices = [float(d.get('price', 0)) for d in historical_data]
            if len(prices) < periods + 1:
                return 0.0

            current_price = prices[-1]
            past_price = prices[-periods-1]

            # Safe division to prevent division by zero
            momentum = (current_price - past_price) / max(past_price, 0.001) if past_price > 0 else 0
            return float(momentum)
        except:
            return 0.0

    def _calculate_support_resistance(self, prices: List[float]) -> Tuple[float, float]:
        """Calculate support and resistance levels"""
        try:
            if len(prices) < 20:
                return min(prices), max(prices)

            # Simple support/resistance using recent highs and lows
            recent_prices = prices[-50:]
            support = np.percentile(recent_prices, 10)
            resistance = np.percentile(recent_prices, 90)

            return float(support), float(resistance)
        except:
            return 0.0, 0.0

    def _calculate_trend_strength(self, prices: List[float]) -> float:
        """Calculate trend strength"""
        try:
            if len(prices) < 20:
                return 0.0

            # Linear regression slope as trend strength
            x = np.arange(len(prices))
            slope, _ = np.polyfit(x, prices, 1)

            # Normalize by average price (safe division)
            avg_price = np.mean(prices)
            trend_strength = slope / max(avg_price, 0.001) if avg_price > 0 else 0

            return float(trend_strength)
        except:
            return 0.0

    def _calculate_liquidity_score(self, order_book: Dict) -> float:
        """Calculate liquidity score from order book"""
        try:
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])

            if not bids or not asks:
                return 0.5

            # Calculate total volume in top 10 levels
            bid_volume = sum(float(bid[1]) for bid in bids[:10])
            ask_volume = sum(float(ask[1]) for ask in asks[:10])

            total_volume = bid_volume + ask_volume
            return min(total_volume / 1000000, 1.0)  # Normalize
        except:
            return 0.5

    def _calculate_order_book_imbalance(self, order_book: Dict) -> float:
        """Calculate order book imbalance"""
        try:
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])

            if not bids or not asks:
                return 0.0

            bid_volume = sum(float(bid[1]) for bid in bids[:5])
            ask_volume = sum(float(ask[1]) for ask in asks[:5])

            total_volume = bid_volume + ask_volume
            if total_volume == 0:
                return 0.0

            imbalance = (bid_volume - ask_volume) / total_volume
            return float(imbalance)
        except:
            return 0.0

    async def _record_signal_for_learning(self, signal: SignalData, features: MarketFeatures):
        """Record signal for future learning and adaptation"""
        try:
            self.performance_history.append({
                'signal': signal,
                'features': features.to_array(),
                'regime': self.current_regime.value,
                'parameters': self.params.to_dict(),
                'timestamp': datetime.now()
            })
        except Exception as e:
            logger.error(f"Error recording signal for learning: {e}")

    async def _enhance_signal_with_neural(self, signal: SignalData, features: MarketFeatures) -> SignalData:
        """Enhance signal using neural network analysis"""
        try:
            if not self.neural_enabled or not self.neural_agent:
                return signal

            # Neural enhancement could adjust confidence, position size, etc.
            # This is a placeholder for more sophisticated neural enhancement
            enhanced_confidence = min(signal.confidence * 1.1, 1.0)
            signal.confidence = enhanced_confidence

            return signal
        except Exception as e:
            logger.error(f"Error enhancing signal with neural network: {e}")
            return signal

    # Implement abstract methods from BaseStrategy
    async def analyze(self, market_context: MarketContext) -> List[SignalData]:
        """Analyze market and return list of signals"""
        signal = await self.analyze_market(market_context)
        return [signal] if signal else []

    async def calculate_position_size(self, signal: SignalData, market_context: MarketContext) -> Decimal:
        """Calculate position size for the signal"""
        try:
            # Extract market features for position sizing - CRITICAL FIX
            current_data = {}
            price = 0.0

            # Handle different market context data structures
            if hasattr(market_context, 'price_data') and market_context.price_data:
                current_data = market_context.price_data.get('coinbase', {}).get('BTC-USD', {})
                price = float(current_data.get('price', 0))

            # Fallback to default price if no data available
            if price <= 0:
                price = 50000.0  # Default BTC price for calculation

            # Create simplified features for position calculation
            def safe_float(value, default=0.0):
                try:
                    if isinstance(value, (int, float)):
                        return float(value)
                    elif isinstance(value, str):
                        return float(value)
                    else:
                        return default
                except (ValueError, TypeError):
                    return default

            # CRITICAL FIX: Safe access to technical indicators and sentiment data
            tech_indicators = {}
            sentiment_data = {}

            if hasattr(market_context, 'technical_indicators') and market_context.technical_indicators:
                tech_indicators = market_context.technical_indicators
            if hasattr(market_context, 'sentiment_data') and market_context.sentiment_data:
                sentiment_data = market_context.sentiment_data

            features = MarketFeatures(
                price=price,
                volume=safe_float(current_data.get('volume', 0)),
                volatility=safe_float(tech_indicators.get('volatility', 0.03)),
                rsi=safe_float(tech_indicators.get('rsi', 50)),
                macd=safe_float(tech_indicators.get('macd', 0)),
                macd_signal=safe_float(tech_indicators.get('macd_signal', 0)),
                bollinger_upper=0, bollinger_lower=0, bollinger_position=0.5,
                momentum_1m=0, momentum_5m=0, momentum_15m=0, volume_ratio=1,
                price_change_1h=0, price_change_4h=0, price_change_24h=0,
                support_level=0, resistance_level=0, trend_strength=0,
                market_sentiment=safe_float(sentiment_data.get('overall', 0)),
                news_impact=0, cross_exchange_spread=0, liquidity_score=0.5, order_book_imbalance=0
            )

            # Calculate adaptive position size
            position_size = self._calculate_adaptive_position_size(features, signal.confidence)

            return Decimal(str(position_size))

        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return Decimal('0.001')  # Safe fallback

    def _should_adapt(self) -> bool:
        """Determine if strategy parameters should be adapted"""
        try:
            current_time = datetime.now()

            # Check if enough time has passed since last adaptation
            time_since_last = current_time - self.last_adaptation
            if time_since_last < self.adaptation_interval:
                return False

            # Check if performance triggers adaptation
            if len(self.performance_history) >= 10:
                recent_performance = list(self.performance_history)[-10:]
                win_rate = sum(1 for p in recent_performance if p > 0) / len(recent_performance)

                # Trigger adaptation if win rate is below threshold
                if win_rate < self.adaptation_triggers['poor_performance']:
                    self._should_adapt_flag = True
                    logger.info(f"🔄 [ADAPTATION] Triggered by poor performance: {win_rate:.2f}")
                    return True

            # Check adaptation counter
            self._adaptation_counter += 1
            if self._adaptation_counter >= 50:  # Adapt every 50 calls
                self._adaptation_counter = 0
                self._should_adapt_flag = True
                logger.info(f"🔄 [ADAPTATION] Triggered by counter")
                return True

            return self._should_adapt_flag

        except Exception as e:
            logger.error(f"Error checking if should adapt: {e}")
            return False
