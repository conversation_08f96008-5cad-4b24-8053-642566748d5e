"""
Binance Exchange Client for Professional Trading
Implements Binance Spot and Futures API with advanced features
"""

import asyncio
import logging
import time
import hmac
import hashlib
import json
import aiohttp
from typing import Dict, List, Optional, Tuple, Any, Union
from decimal import Decimal
from urllib.parse import urlencode
from src.utils.cryptography.secure_credentials import decrypt_value

logger = logging.getLogger(__name__)

class BinanceClient:
    """Professional Binance client with advanced trading features"""
    
    def __init__(self, api_key: str = None, api_secret: str = None, testnet: bool = False):
        self.api_key = api_key
        self.api_secret = api_secret
        self.testnet = testnet
        
        # API endpoints
        if testnet:
            self.base_url = "https://testnet.binance.vision/api"
            self.ws_url = "wss://testnet.binance.vision/ws"
        else:
            self.base_url = "https://api.binance.com/api"
            self.ws_url = "wss://stream.binance.com:9443/ws"
        
        # Rate limiting
        self.request_weight = 0
        self.last_request_time = 0
        self.weight_reset_time = 0
        
        # Connection pool
        self.session = None
        self.connector = None
        
        # Market data cache
        self.symbol_info = {}
        self.price_cache = {}
        self.last_price_update = 0
        
        logger.info(f"🟡 [BINANCE] Client initialized ({'testnet' if testnet else 'live'})")

    async def initialize(self):
        """Initialize the client with connection pool"""
        try:
            # Create connection pool
            self.connector = aiohttp.TCPConnector(
                limit=100,
                limit_per_host=30,
                ttl_dns_cache=300,
                use_dns_cache=True,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )
            
            self.session = aiohttp.ClientSession(
                connector=self.connector,
                timeout=aiohttp.ClientTimeout(total=30),
                headers={
                    'User-Agent': 'AutoGPT-Trader/1.0',
                    'Content-Type': 'application/json'
                }
            )
            
            # Load market info
            await self._load_exchange_info()
            
            logger.info("✅ [BINANCE] Client initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ [BINANCE] Initialization failed: {e}")
            return False

    async def close(self):
        """Close the client and cleanup resources"""
        if self.session:
            await self.session.close()
        if self.connector:
            await self.connector.close()

    def _generate_signature(self, query_string: str) -> str:
        """Generate HMAC SHA256 signature for authenticated requests"""
        if not self.api_secret:
            raise ValueError("API secret is required for authenticated requests")

        return hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

    async def _make_request(self, method: str, endpoint: str, params: Dict = None, 
                           signed: bool = False) -> Dict:
        """Make authenticated API request with rate limiting"""
        if params is None:
            params = {}
        
        # Add timestamp for signed requests
        if signed:
            params['timestamp'] = int(time.time() * 1000)
            params['recvWindow'] = 5000
        
        # Create query string
        query_string = urlencode(sorted(params.items()))
        
        # Add signature for signed requests
        if signed:
            signature = self._generate_signature(query_string)
            query_string += f"&signature={signature}"
        
        # Build URL
        url = f"{self.base_url}{endpoint}"
        if query_string:
            url += f"?{query_string}"
        
        # Prepare headers
        headers = {}
        if signed:
            if not self.api_key:
                raise ValueError("API key is required for authenticated requests")
            headers['X-MBX-APIKEY'] = self.api_key
        
        # Rate limiting check
        await self._check_rate_limit()
        
        try:
            async with self.session.request(method, url, headers=headers) as response:
                self._update_rate_limit(response.headers)
                
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    logger.error(f"❌ [BINANCE] API error {response.status}: {error_text}")
                    raise Exception(f"Binance API error: {response.status} - {error_text}")
                    
        except Exception as e:
            logger.error(f"❌ [BINANCE] Request failed: {e}")
            raise

    async def _check_rate_limit(self):
        """Check and enforce rate limits"""
        current_time = time.time()
        
        # Reset weight if minute has passed
        if current_time - self.weight_reset_time >= 60:
            self.request_weight = 0
            self.weight_reset_time = current_time
        
        # Check if we're approaching limits
        if self.request_weight >= 1000:  # Binance limit is 1200/minute
            sleep_time = 60 - (current_time - self.weight_reset_time)
            if sleep_time > 0:
                logger.warning(f"⚠️ [BINANCE] Rate limit approaching, sleeping {sleep_time:.2f}s")
                await asyncio.sleep(sleep_time)
                self.request_weight = 0
                self.weight_reset_time = time.time()

    def _update_rate_limit(self, headers: Dict):
        """Update rate limit tracking from response headers"""
        if 'x-mbx-used-weight-1m' in headers:
            self.request_weight = int(headers['x-mbx-used-weight-1m'])

    async def _load_exchange_info(self):
        """Load exchange information and symbol details"""
        try:
            info = await self._make_request('GET', '/v3/exchangeInfo')
            
            for symbol_data in info['symbols']:
                symbol = symbol_data['symbol']
                self.symbol_info[symbol] = {
                    'status': symbol_data['status'],
                    'baseAsset': symbol_data['baseAsset'],
                    'quoteAsset': symbol_data['quoteAsset'],
                    'filters': {f['filterType']: f for f in symbol_data['filters']},
                    'permissions': symbol_data['permissions']
                }
            
            logger.info(f"✅ [BINANCE] Loaded {len(self.symbol_info)} symbols")
            
        except Exception as e:
            logger.error(f"❌ [BINANCE] Failed to load exchange info: {e}")

    async def get_balance(self, asset: str = None) -> Union[Dict, Decimal]:
        """Get account balance"""
        try:
            account_info = await self._make_request('GET', '/v3/account', signed=True)
            
            if asset:
                for balance in account_info['balances']:
                    if balance['asset'] == asset:
                        return Decimal(balance['free'])
                return Decimal('0')
            else:
                balances = {}
                for balance in account_info['balances']:
                    free = Decimal(balance['free'])
                    if free > 0:
                        balances[balance['asset']] = free
                return balances
                
        except Exception as e:
            logger.error(f"❌ [BINANCE] Failed to get balance: {e}")
            return Decimal('0') if asset else {}

    async def get_price(self, symbol: str) -> Optional[Decimal]:
        """Get current price for symbol"""
        try:
            # Check cache first
            current_time = time.time()
            if (symbol in self.price_cache and 
                current_time - self.last_price_update < 5):  # 5 second cache
                return self.price_cache[symbol]
            
            # Fetch from API
            response = await self._make_request('GET', '/v3/ticker/price', {'symbol': symbol})
            price = Decimal(response['price'])
            
            # Update cache
            self.price_cache[symbol] = price
            self.last_price_update = current_time
            
            return price
            
        except Exception as e:
            logger.error(f"❌ [BINANCE] Failed to get price for {symbol}: {e}")
            return None

    async def get_order_book(self, symbol: str, limit: int = 100) -> Dict:
        """Get order book for symbol"""
        try:
            params = {'symbol': symbol, 'limit': limit}
            response = await self._make_request('GET', '/v3/depth', params)

            return {
                'bids': [(Decimal(price), Decimal(qty)) for price, qty in response['bids']],
                'asks': [(Decimal(price), Decimal(qty)) for price, qty in response['asks']],
                'timestamp': response['lastUpdateId']
            }

        except Exception as e:
            logger.error(f"❌ [BINANCE] Failed to get order book for {symbol}: {e}")
            return {'bids': [], 'asks': [], 'timestamp': 0}

    async def get_24hr_ticker(self, symbol: str) -> Dict:
        """Get 24hr ticker statistics"""
        try:
            params = {'symbol': symbol}
            response = await self._make_request('GET', '/v3/ticker/24hr', params)

            return {
                'symbol': response['symbol'],
                'price_change': Decimal(response['priceChange']),
                'price_change_percent': Decimal(response['priceChangePercent']),
                'weighted_avg_price': Decimal(response['weightedAvgPrice']),
                'prev_close_price': Decimal(response['prevClosePrice']),
                'last_price': Decimal(response['lastPrice']),
                'bid_price': Decimal(response['bidPrice']),
                'ask_price': Decimal(response['askPrice']),
                'open_price': Decimal(response['openPrice']),
                'high_price': Decimal(response['highPrice']),
                'low_price': Decimal(response['lowPrice']),
                'volume': Decimal(response['volume']),
                'quote_volume': Decimal(response['quoteVolume']),
                'open_time': response['openTime'],
                'close_time': response['closeTime'],
                'count': response['count']
            }

        except Exception as e:
            logger.error(f"❌ [BINANCE] Failed to get 24hr ticker for {symbol}: {e}")
            return {}

    def _validate_order_params(self, symbol: str, side: str, order_type: str,
                              quantity: Decimal, price: Decimal = None) -> bool:
        """Validate order parameters against symbol filters"""
        try:
            if symbol not in self.symbol_info:
                logger.error(f"❌ [BINANCE] Unknown symbol: {symbol}")
                return False

            symbol_data = self.symbol_info[symbol]
            filters = symbol_data['filters']

            # Check symbol status
            if symbol_data['status'] != 'TRADING':
                logger.error(f"❌ [BINANCE] Symbol {symbol} not trading")
                return False

            # Check lot size filter
            if 'LOT_SIZE' in filters:
                lot_filter = filters['LOT_SIZE']
                min_qty = Decimal(lot_filter['minQty'])
                max_qty = Decimal(lot_filter['maxQty'])
                step_size = Decimal(lot_filter['stepSize'])

                if quantity < min_qty or quantity > max_qty:
                    logger.error(f"❌ [BINANCE] Quantity {quantity} outside range [{min_qty}, {max_qty}]")
                    return False

                # Check step size
                if (quantity - min_qty) % step_size != 0:
                    logger.error(f"❌ [BINANCE] Quantity {quantity} doesn't match step size {step_size}")
                    return False

            # Check price filter for limit orders
            if price and 'PRICE_FILTER' in filters:
                price_filter = filters['PRICE_FILTER']
                min_price = Decimal(price_filter['minPrice'])
                max_price = Decimal(price_filter['maxPrice'])
                tick_size = Decimal(price_filter['tickSize'])

                if price < min_price or price > max_price:
                    logger.error(f"❌ [BINANCE] Price {price} outside range [{min_price}, {max_price}]")
                    return False

                # Check tick size
                if (price - min_price) % tick_size != 0:
                    logger.error(f"❌ [BINANCE] Price {price} doesn't match tick size {tick_size}")
                    return False

            # Check notional filter
            if 'MIN_NOTIONAL' in filters:
                notional_filter = filters['MIN_NOTIONAL']
                min_notional = Decimal(notional_filter['minNotional'])

                if price:
                    notional = quantity * price
                else:
                    # For market orders, skip notional validation for now
                    # (would need async call to get current price)
                    logger.debug(f"⚠️ [BINANCE] Skipping notional validation for market order on {symbol}")
                    return True

                if notional < min_notional:
                    logger.error(f"❌ [BINANCE] Notional {notional} below minimum {min_notional}")
                    return False

            return True

        except Exception as e:
            logger.error(f"❌ [BINANCE] Order validation failed: {e}")
            return False

    async def place_order(self, symbol: str, side: str, order_type: str,
                         quantity: Decimal, price: Decimal = None,
                         time_in_force: str = 'GTC', **kwargs) -> Dict:
        """Place a trading order"""
        try:
            # Validate parameters
            if not self._validate_order_params(symbol, side, order_type, quantity, price):
                raise ValueError("Order validation failed")

            # Prepare order parameters
            params = {
                'symbol': symbol,
                'side': side.upper(),
                'type': order_type.upper(),
                'quantity': str(quantity)
            }

            # Add price for limit orders
            if order_type.upper() in ['LIMIT', 'STOP_LOSS_LIMIT', 'TAKE_PROFIT_LIMIT']:
                if not price:
                    raise ValueError(f"Price required for {order_type} orders")
                params['price'] = str(price)
                params['timeInForce'] = time_in_force

            # Add additional parameters
            for key, value in kwargs.items():
                if value is not None:
                    params[key] = str(value)

            logger.warning(f"💰 [BINANCE] Placing {side} {order_type} order: {symbol} {quantity} @ {price or 'market'}")

            # Execute order
            response = await self._make_request('POST', '/v3/order', params, signed=True)

            logger.info(f"✅ [BINANCE] Order placed successfully: {response['orderId']}")

            return {
                'order_id': response['orderId'],
                'symbol': response['symbol'],
                'side': response['side'],
                'type': response['type'],
                'quantity': Decimal(response['origQty']),
                'price': Decimal(response['price']) if response.get('price') else None,
                'status': response['status'],
                'time_in_force': response.get('timeInForce'),
                'fills': response.get('fills', []),
                'commission': sum(Decimal(fill['commission']) for fill in response.get('fills', [])),
                'timestamp': response['transactTime']
            }

        except Exception as e:
            logger.error(f"❌ [BINANCE] Failed to place order: {e}")
            raise

    async def cancel_order(self, symbol: str, order_id: int) -> Dict:
        """Cancel an existing order"""
        try:
            params = {
                'symbol': symbol,
                'orderId': order_id
            }

            response = await self._make_request('DELETE', '/v3/order', params, signed=True)

            logger.info(f"✅ [BINANCE] Order {order_id} cancelled successfully")

            return {
                'order_id': response['orderId'],
                'symbol': response['symbol'],
                'status': response['status'],
                'orig_quantity': Decimal(response['origQty']),
                'executed_quantity': Decimal(response['executedQty'])
            }

        except Exception as e:
            logger.error(f"❌ [BINANCE] Failed to cancel order {order_id}: {e}")
            raise

    async def get_order_status(self, symbol: str, order_id: int) -> Dict:
        """Get order status"""
        try:
            params = {
                'symbol': symbol,
                'orderId': order_id
            }

            response = await self._make_request('GET', '/v3/order', params, signed=True)

            return {
                'order_id': response['orderId'],
                'symbol': response['symbol'],
                'side': response['side'],
                'type': response['type'],
                'quantity': Decimal(response['origQty']),
                'price': Decimal(response['price']) if response.get('price') else None,
                'executed_quantity': Decimal(response['executedQty']),
                'status': response['status'],
                'time_in_force': response.get('timeInForce'),
                'timestamp': response['time'],
                'update_time': response['updateTime']
            }

        except Exception as e:
            logger.error(f"❌ [BINANCE] Failed to get order status: {e}")
            raise

    async def get_open_orders(self, symbol: str = None) -> List[Dict]:
        """Get all open orders"""
        try:
            params = {}
            if symbol:
                params['symbol'] = symbol

            response = await self._make_request('GET', '/v3/openOrders', params, signed=True)

            orders = []
            for order in response:
                orders.append({
                    'order_id': order['orderId'],
                    'symbol': order['symbol'],
                    'side': order['side'],
                    'type': order['type'],
                    'quantity': Decimal(order['origQty']),
                    'price': Decimal(order['price']) if order.get('price') else None,
                    'executed_quantity': Decimal(order['executedQty']),
                    'status': order['status'],
                    'time_in_force': order.get('timeInForce'),
                    'timestamp': order['time']
                })

            return orders

        except Exception as e:
            logger.error(f"❌ [BINANCE] Failed to get open orders: {e}")
            return []

    async def get_trade_history(self, symbol: str, limit: int = 500) -> List[Dict]:
        """Get trade history for symbol"""
        try:
            params = {
                'symbol': symbol,
                'limit': limit
            }

            response = await self._make_request('GET', '/v3/myTrades', params, signed=True)

            trades = []
            for trade in response:
                trades.append({
                    'trade_id': trade['id'],
                    'order_id': trade['orderId'],
                    'symbol': trade['symbol'],
                    'side': 'BUY' if trade['isBuyer'] else 'SELL',
                    'quantity': Decimal(trade['qty']),
                    'price': Decimal(trade['price']),
                    'commission': Decimal(trade['commission']),
                    'commission_asset': trade['commissionAsset'],
                    'timestamp': trade['time'],
                    'is_maker': trade['isMaker']
                })

            return trades

        except Exception as e:
            logger.error(f"❌ [BINANCE] Failed to get trade history: {e}")
            return []
