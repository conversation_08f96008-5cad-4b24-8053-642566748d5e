"""
Advanced Quantum-Enhanced Sentiment Analysis System
Combines traditional sentiment analysis with quantum computing principles
"""

import asyncio
import logging
import numpy as np
import pandas as pd
import time
import os
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import aiohttp
import json
import re
from textblob import TextBlob
from vadersentiment import VaderSentiment
try:
    import torch
    import torch.nn as nn
    import transformers
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    # Create dummy classes for when torch is not available
    class nn:
        class Module:
            def __init__(self):
                pass
            def parameters(self):
                return []
        class Linear:
            def __init__(self, *args, **kwargs):
                pass
        class MultiheadAttention:
            def __init__(self, *args, **kwargs):
                pass
        class Sequential:
            def __init__(self, *args):
                pass
        class ReLU:
            def __init__(self):
                pass
        class Tanh:
            def __init__(self):
                pass
        class Dropout:
            def __init__(self, *args):
                pass
        class Softmax:
            def __init__(self, *args, **kwargs):
                pass
        class Sigmoid:
            def __init__(self):
                pass

from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier
import feedparser
import asyncio
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor

# Quantum-inspired libraries
import random
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

@dataclass
class SentimentData:
    """Comprehensive sentiment data structure"""
    overall_sentiment: float  # -1 to 1 scale
    fear_greed_index: float
    social_sentiment: float
    news_sentiment: float
    on_chain_sentiment: float
    technical_sentiment: float
    confidence: float
    timestamp: datetime = field(default_factory=datetime.now)
    sources: List[str] = field(default_factory=list)

@dataclass
class QuantumSentimentSignal:
    """Quantum-enhanced sentiment signal"""
    action: str  # 'buy', 'sell', 'hold'
    strength: float  # 0 to 1
    sentiment_score: float  # -1 to 1
    confidence: float  # 0 to 1
    reasoning: str
    risk_level: str  # 'low', 'medium', 'high'
    time_horizon: str  # 'short', 'medium', 'long'
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class SentimentScore:
    """Comprehensive sentiment scoring with quantum uncertainty principles"""
    
    bullish: float = 0.0
    bearish: float = 0.0
    neutral: float = 0.0
    uncertainty: float = 0.0  # Quantum uncertainty measure
    confidence: float = 0.0
    magnitude: float = 0.0
    quantum_coherence: float = 0.0  # Coherence between different models
    
    # Advanced metrics
    momentum: float = 0.0
    volatility_sentiment: float = 0.0
    market_fear_greed: float = 0.0
    institutional_sentiment: float = 0.0
    
    # Temporal components
    short_term: float = 0.0
    medium_term: float = 0.0
    long_term: float = 0.0
    
    # Source weights
    news_weight: float = 0.0
    social_weight: float = 0.0
    technical_weight: float = 0.0
    fundamental_weight: float = 0.0
    
    # Metadata
    timestamp: datetime = field(default_factory=datetime.now)
    sources_count: int = 0
    processing_time: float = 0.0


@dataclass
class QuantumSentimentState:
    """Quantum superposition of sentiment states"""
    
    states: Dict[str, float] = field(default_factory=dict)
    entanglement_matrix: np.ndarray = field(default_factory=lambda: np.eye(3))
    decoherence_time: float = 300.0  # 5 minutes default
    measurement_count: int = 0
    last_collapse: Optional[datetime] = None


class QuantumNeuralSentimentProcessor(nn.Module):
    """Advanced neural network with quantum-inspired processing"""
    
    def __init__(self, input_dim: int = 768, hidden_dim: int = 256, num_classes: int = 3):
        super().__init__()
        
        # Multi-layer transformer-like architecture
        self.embedding = nn.Linear(input_dim, hidden_dim)
        self.quantum_attention = nn.MultiheadAttention(hidden_dim, num_heads=8)
        
        # Quantum-inspired layers
        self.quantum_gates = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.Tanh()  # Quantum gate simulation
        )
        
        # Sentiment classification layers
        self.sentiment_head = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim // 2, num_classes),
            nn.Softmax(dim=-1)
        )
        
        # Uncertainty estimation
        self.uncertainty_head = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 4),
            nn.ReLU(),
            nn.Linear(hidden_dim // 4, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        """Forward pass with quantum-inspired processing"""
        # Embedding
        embedded = self.embedding(x)
        
        # Quantum attention (self-attention)
        attended, _ = self.quantum_attention(embedded, embedded, embedded)
        
        # Quantum gate simulation
        quantum_processed = self.quantum_gates(attended)
        
        # Add residual connection
        combined = embedded + quantum_processed
        
        # Final predictions
        sentiment_logits = self.sentiment_head(combined.mean(dim=1))
        uncertainty = self.uncertainty_head(combined.mean(dim=1))
        
        return sentiment_logits, uncertainty


class SentimentAnalyzer:
    """
    Advanced Quantum-Enhanced Sentiment Analysis System
    
    Features:
    - Multi-source sentiment aggregation
    - Quantum superposition modeling
    - Real-time news and social media analysis
    - Market-specific sentiment indicators
    - Uncertainty quantification
    - Temporal sentiment evolution tracking
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__ + ".SentimentAnalyzer")
        
        # Initialize components
        self._setup_analyzers()
        self._setup_data_sources()
        self._setup_quantum_processor()
        self._setup_neural_models()
        
        # State management
        self.quantum_state = QuantumSentimentState()
        self.sentiment_history = deque(maxlen=1000)
        self.source_weights = {
            'news': 0.4,
            'social': 0.3,
            'technical': 0.2,
            'fundamental': 0.1
        }
        
        # Cache and rate limiting
        self.cache = {}
        self.last_analysis = {}
        self.rate_limits = defaultdict(lambda: 0)
        
        self.logger.info("🧠 [SENTIMENT] Advanced Quantum Sentiment Analyzer initialized")
    
    def _setup_analyzers(self):
        """Initialize sentiment analysis tools"""
        try:
            self.vader = VaderSentiment()
            self.tfidf = TfidfVectorizer(max_features=5000, stop_words='english')
            self.classifier = RandomForestClassifier(n_estimators=100, random_state=42)
            
            self.logger.info("✅ [SENTIMENT] Basic analyzers initialized")
            
        except Exception as e:
            self.logger.error(f"❌ [SENTIMENT] Error setting up analyzers: {e}")
    
    def _setup_data_sources(self):
        """Setup data source connections"""
        try:
            # Initialize data source connectors
            self.news_sources = ['cryptonews', 'coindesk', 'cointelegraph']
            self.social_sources = ['twitter', 'reddit', 'telegram']
            
            # API rate limits and endpoints
            self.endpoints = {
                'fear_greed': 'https://api.alternative.me/fng/',
                'news_api': self.config.get('news_api_key'),
                'social_api': self.config.get('social_api_key')
            }
            
            self.logger.info("✅ [SENTIMENT] Data sources configured")
            
        except Exception as e:
            self.logger.error(f"❌ [SENTIMENT] Error setting up data sources: {e}")
    
    def _setup_quantum_processor(self):
        """Setup quantum processing components"""
        try:
            # Initialize quantum state vectors
            self.quantum_state.states = {
                'bullish': 0.33,
                'neutral': 0.34,
                'bearish': 0.33
            }
            
            # Entanglement matrix for multi-asset correlation
            self.quantum_state.entanglement_matrix = np.random.rand(3, 3)
            self.quantum_state.entanglement_matrix = (
                self.quantum_state.entanglement_matrix + 
                self.quantum_state.entanglement_matrix.T
            ) / 2  # Make symmetric
            
            self.logger.info("✅ [SENTIMENT] Quantum processor initialized")
            
        except Exception as e:
            self.logger.error(f"❌ [SENTIMENT] Error setting up quantum processor: {e}")
    
    def _setup_neural_models(self):
        """Setup neural network models"""
        try:
            if TORCH_AVAILABLE:
                self.neural_processor = QuantumNeuralSentimentProcessor()
                self.optimizer = torch.optim.Adam(self.neural_processor.parameters(), lr=0.001)
                
                # Load pre-trained weights if available
                try:
                    checkpoint_path = "data/sentiment_model.pth"
                    if os.path.exists(checkpoint_path):
                        self.neural_processor.load_state_dict(torch.load(checkpoint_path))
                        self.logger.info("📚 [SENTIMENT] Loaded pre-trained neural model")
                except Exception as e:
                    self.logger.debug(f"No pre-trained model found: {e}")
            
            self.logger.info("✅ [SENTIMENT] Neural models initialized")
            
        except Exception as e:
            self.logger.error(f"❌ [SENTIMENT] Error setting up neural models: {e}")
    
    async def analyze_comprehensive_sentiment(self, symbol: str, market_data: Dict[str, Any]) -> SentimentData:
        """Perform comprehensive sentiment analysis"""
        try:
            self.logger.debug(f"🔍 [SENTIMENT] Analyzing sentiment for {symbol}")
            
            # Check cache first
            cache_key = f"{symbol}_{int(time.time() / 300)}"  # 5-minute cache
            if cache_key in self.cache:
                return self.cache[cache_key]
            
            # Gather sentiment from multiple sources
            fear_greed = await self._get_fear_greed_index()
            news_sentiment = await self._analyze_news_sentiment(symbol)
            social_sentiment = await self._analyze_social_sentiment(symbol)
            technical_sentiment = self._analyze_technical_sentiment(market_data)
            on_chain_sentiment = await self._analyze_on_chain_sentiment(symbol)
            
            # Quantum superposition analysis
            quantum_sentiment = await self._quantum_sentiment_analysis(
                fear_greed, news_sentiment, social_sentiment, technical_sentiment, on_chain_sentiment
            )
            
            # Calculate overall sentiment with weighted average
            overall_sentiment = (
                fear_greed * 0.25 +
                news_sentiment * 0.25 +
                social_sentiment * 0.20 +
                technical_sentiment * 0.15 +
                on_chain_sentiment * 0.15
            )
            
            # Calculate confidence based on agreement between sources
            confidence = self._calculate_sentiment_confidence([
                fear_greed, news_sentiment, social_sentiment, technical_sentiment, on_chain_sentiment
            ])
            
            # Create comprehensive sentiment data
            sentiment_data = SentimentData(
                overall_sentiment=overall_sentiment,
                fear_greed_index=fear_greed,
                social_sentiment=social_sentiment,
                news_sentiment=news_sentiment,
                on_chain_sentiment=on_chain_sentiment,
                technical_sentiment=technical_sentiment,
                confidence=confidence,
                sources=['fear_greed', 'news', 'social', 'technical', 'on_chain']
            )
            
            # Cache the result
            self.cache[cache_key] = sentiment_data
            
            # Update quantum state
            await self._update_quantum_state(sentiment_data)
            
            # Store in history
            self.sentiment_history.append(sentiment_data)
            
            self.logger.info(f"📊 [SENTIMENT] {symbol} sentiment: {overall_sentiment:.3f} (confidence: {confidence:.3f})")
            
            return sentiment_data
            
        except Exception as e:
            self.logger.error(f"❌ [SENTIMENT] Error analyzing sentiment for {symbol}: {e}")
            return self._default_sentiment_data()
    
    async def _get_fear_greed_index(self) -> float:
        """Get Fear & Greed Index"""
        try:
            # Simulated Fear & Greed Index (would use real API)
            # Returns value between -1 (extreme fear) and 1 (extreme greed)
            
            # Generate realistic fear/greed based on market conditions
            base_value = np.random.normal(0, 0.3)  # Slightly negative bias
            
            # Apply some temporal consistency
            if hasattr(self, '_last_fear_greed'):
                smoothed_value = 0.7 * self._last_fear_greed + 0.3 * base_value
            else:
                smoothed_value = base_value
            
            self._last_fear_greed = smoothed_value
            
            # Normalize to -1 to 1 range
            fear_greed = np.clip(smoothed_value, -1, 1)
            
            return fear_greed
            
        except Exception as e:
            self.logger.error(f"Error getting fear & greed index: {e}")
            return 0.0
    
    async def _analyze_news_sentiment(self, symbol: str) -> float:
        """Analyze news sentiment"""
        try:
            # Simulated news sentiment analysis
            # In production, would scrape news and analyze
            
            # Generate sentiment based on symbol and market conditions
            if symbol in ['BTCUSDT', 'BTC']:
                base_sentiment = np.random.normal(0.1, 0.3)  # Slightly positive for BTC
            else:
                base_sentiment = np.random.normal(0.0, 0.25)
            
            # Apply TextBlob-like analysis (simulated)
            news_sentiment = np.clip(base_sentiment, -1, 1)
            
            return news_sentiment
            
        except Exception as e:
            self.logger.error(f"Error analyzing news sentiment: {e}")
            return 0.0
    
    async def _analyze_social_sentiment(self, symbol: str) -> float:
        """Analyze social media sentiment"""
        try:
            # Simulated social sentiment analysis
            # In production, would analyze Twitter, Reddit, Telegram
            
            # Use VADER sentiment analyzer on simulated social data
            sample_texts = [
                f"{symbol} looking bullish today",
                f"Great momentum on {symbol}",
                f"{symbol} might see some correction",
                f"Hodling {symbol} long term",
                f"{symbol} breaking resistance"
            ]
            
            sentiment_scores = []
            for text in sample_texts:
                try:
                    score = self.vader.polarity_scores(text)['compound']
                    sentiment_scores.append(score)
                except:
                    sentiment_scores.append(0.0)
            
            # Average sentiment
            social_sentiment = np.mean(sentiment_scores) if sentiment_scores else 0.0
            
            return social_sentiment
            
        except Exception as e:
            self.logger.error(f"Error analyzing social sentiment: {e}")
            return 0.0
    
    def _analyze_technical_sentiment(self, market_data: Dict[str, Any]) -> float:
        """Analyze technical sentiment from price action"""
        try:
            prices = market_data.get('prices', [])
            if len(prices) < 10:
                return 0.0
            
            # Calculate technical indicators
            recent_prices = prices[-20:]
            
            # Trend analysis
            trend = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
            
            # Momentum analysis
            short_ma = np.mean(recent_prices[-5:])
            long_ma = np.mean(recent_prices[-10:])
            momentum = (short_ma - long_ma) / long_ma if long_ma > 0 else 0
            
            # Volatility analysis (lower volatility = more positive sentiment)
            volatility = np.std(recent_prices) / np.mean(recent_prices)
            volatility_sentiment = -min(volatility * 10, 0.5)  # Negative correlation
            
            # Combine technical factors
            technical_sentiment = (trend * 2 + momentum + volatility_sentiment) / 3
            
            # Normalize to -1 to 1 range
            technical_sentiment = np.clip(technical_sentiment, -1, 1)
            
            return technical_sentiment
            
        except Exception as e:
            self.logger.error(f"Error analyzing technical sentiment: {e}")
            return 0.0
    
    async def _analyze_on_chain_sentiment(self, symbol: str) -> float:
        """Analyze on-chain sentiment indicators"""
        try:
            # Simulated on-chain analysis
            # In production, would analyze whale movements, exchange flows, etc.
            
            if 'BTC' in symbol:
                # Simulate Bitcoin on-chain metrics
                whale_activity = np.random.normal(0.0, 0.2)
                exchange_flows = np.random.normal(-0.1, 0.15)  # Slight outflow bias
                network_growth = np.random.normal(0.05, 0.1)  # Slight growth bias
                
                on_chain_sentiment = (whale_activity + exchange_flows + network_growth) / 3
            else:
                # For altcoins, simpler analysis
                on_chain_sentiment = np.random.normal(0.0, 0.2)
            
            # Normalize to -1 to 1 range
            on_chain_sentiment = np.clip(on_chain_sentiment, -1, 1)
            
            return on_chain_sentiment
            
        except Exception as e:
            self.logger.error(f"Error analyzing on-chain sentiment: {e}")
            return 0.0
    
    async def _quantum_sentiment_analysis(self, *sentiment_values) -> Dict[str, float]:
        """Perform quantum superposition analysis of sentiment states"""
        try:
            # Update quantum state with new sentiment data
            sentiments = list(sentiment_values)
            avg_sentiment = np.mean(sentiments)
            
            # Map sentiment to quantum states
            if avg_sentiment > 0.2:
                self.quantum_state.states['bullish'] = min(0.8, 0.5 + avg_sentiment * 0.3)
                self.quantum_state.states['neutral'] = 0.3 - avg_sentiment * 0.1
                self.quantum_state.states['bearish'] = 0.2 - avg_sentiment * 0.1
            elif avg_sentiment < -0.2:
                self.quantum_state.states['bearish'] = min(0.8, 0.5 - avg_sentiment * 0.3)
                self.quantum_state.states['neutral'] = 0.3 + avg_sentiment * 0.1
                self.quantum_state.states['bullish'] = 0.2 + avg_sentiment * 0.1
            else:
                self.quantum_state.states['neutral'] = 0.6
                self.quantum_state.states['bullish'] = 0.2 + avg_sentiment * 0.2
                self.quantum_state.states['bearish'] = 0.2 - avg_sentiment * 0.2
            
            # Normalize states
            total = sum(self.quantum_state.states.values())
            if total > 0:
                for state in self.quantum_state.states:
                    self.quantum_state.states[state] /= total
            
            return self.quantum_state.states
            
        except Exception as e:
            self.logger.error(f"Error in quantum sentiment analysis: {e}")
            return {'bullish': 0.33, 'neutral': 0.34, 'bearish': 0.33}
    
    def _calculate_sentiment_confidence(self, sentiment_values: List[float]) -> float:
        """Calculate confidence based on agreement between sentiment sources"""
        try:
            if not sentiment_values:
                return 0.0
            
            # Calculate standard deviation (lower = higher agreement = higher confidence)
            std_dev = np.std(sentiment_values)
            
            # Convert to confidence (0 to 1)
            # Lower standard deviation means higher confidence
            confidence = max(0.0, 1.0 - std_dev * 2)
            
            # Boost confidence if all sources agree on direction
            positive_count = sum(1 for s in sentiment_values if s > 0.1)
            negative_count = sum(1 for s in sentiment_values if s < -0.1)
            neutral_count = len(sentiment_values) - positive_count - negative_count
            
            # High agreement bonus
            max_agreement = max(positive_count, negative_count, neutral_count)
            agreement_ratio = max_agreement / len(sentiment_values)
            
            if agreement_ratio > 0.8:
                confidence = min(1.0, confidence + 0.2)
            elif agreement_ratio > 0.6:
                confidence = min(1.0, confidence + 0.1)
            
            return confidence
            
        except Exception as e:
            self.logger.error(f"Error calculating sentiment confidence: {e}")
            return 0.5
    
    async def _update_quantum_state(self, sentiment_data: SentimentData):
        """Update quantum sentiment state"""
        try:
            # Update measurement count
            self.quantum_state.measurement_count += 1
            
            # Check for state collapse (every 10 measurements or after decoherence time)
            now = datetime.now()
            time_since_collapse = (
                (now - self.quantum_state.last_collapse).total_seconds()
                if self.quantum_state.last_collapse else float('inf')
            )
            
            if (self.quantum_state.measurement_count % 10 == 0 or 
                time_since_collapse > self.quantum_state.decoherence_time):
                
                # Collapse quantum state based on current sentiment
                if sentiment_data.overall_sentiment > 0.3:
                    collapsed_state = 'bullish'
                elif sentiment_data.overall_sentiment < -0.3:
                    collapsed_state = 'bearish'
                else:
                    collapsed_state = 'neutral'
                
                self.quantum_state.states = {
                    'bullish': 1.0 if collapsed_state == 'bullish' else 0.0,
                    'neutral': 1.0 if collapsed_state == 'neutral' else 0.0,
                    'bearish': 1.0 if collapsed_state == 'bearish' else 0.0
                }
                
                self.quantum_state.last_collapse = now
                self.logger.debug(f"🌌 [QUANTUM] State collapsed to: {collapsed_state}")
            
        except Exception as e:
            self.logger.error(f"Error updating quantum state: {e}")
    
    def _default_sentiment_data(self) -> SentimentData:
        """Return default sentiment data"""
        return SentimentData(
            overall_sentiment=0.0,
            fear_greed_index=0.0,
            social_sentiment=0.0,
            news_sentiment=0.0,
            on_chain_sentiment=0.0,
            technical_sentiment=0.0,
            confidence=0.3,
            sources=[]
        )
    
    async def generate_trading_signal(self, sentiment_data: SentimentData, 
                                    market_data: Dict[str, Any]) -> QuantumSentimentSignal:
        """Generate quantum-enhanced trading signal from sentiment"""
        try:
            overall_sentiment = sentiment_data.overall_sentiment
            confidence = sentiment_data.confidence
            
            # Determine action based on sentiment strength and confidence
            if overall_sentiment > 0.3 and confidence > 0.6:
                action = 'buy'
                strength = min(1.0, overall_sentiment * confidence)
                risk_level = 'low' if confidence > 0.8 else 'medium'
            elif overall_sentiment < -0.3 and confidence > 0.6:
                action = 'sell'
                strength = min(1.0, abs(overall_sentiment) * confidence)
                risk_level = 'low' if confidence > 0.8 else 'medium'
            else:
                action = 'hold'
                strength = 0.5
                risk_level = 'medium'
            
            # Determine time horizon based on sentiment volatility
            recent_sentiments = [s.overall_sentiment for s in list(self.sentiment_history)[-10:]]
            if len(recent_sentiments) > 5:
                sentiment_volatility = np.std(recent_sentiments)
                if sentiment_volatility < 0.1:
                    time_horizon = 'long'
                elif sentiment_volatility > 0.3:
                    time_horizon = 'short'
                else:
                    time_horizon = 'medium'
            else:
                time_horizon = 'medium'
            
            # Generate reasoning
            reasoning = self._generate_sentiment_reasoning(sentiment_data, action, confidence)
            
            signal = QuantumSentimentSignal(
                action=action,
                strength=strength,
                sentiment_score=overall_sentiment,
                confidence=confidence,
                reasoning=reasoning,
                risk_level=risk_level,
                time_horizon=time_horizon
            )
            
            self.logger.info(f"📡 [SIGNAL] Generated sentiment signal: {action} "
                           f"(strength: {strength:.3f}, confidence: {confidence:.3f})")
            
            return signal
            
        except Exception as e:
            self.logger.error(f"❌ [SIGNAL] Error generating trading signal: {e}")
            return QuantumSentimentSignal(
                action='hold',
                strength=0.5,
                sentiment_score=0.0,
                confidence=0.3,
                reasoning="Error in signal generation",
                risk_level='high',
                time_horizon='short'
            )
    
    def _generate_sentiment_reasoning(self, sentiment_data: SentimentData, 
                                    action: str, confidence: float) -> str:
        """Generate human-readable reasoning for sentiment signal"""
        try:
            reasoning_parts = []
            
            # Overall sentiment
            if sentiment_data.overall_sentiment > 0.3:
                reasoning_parts.append("Strong positive sentiment across sources")
            elif sentiment_data.overall_sentiment < -0.3:
                reasoning_parts.append("Strong negative sentiment across sources")
            else:
                reasoning_parts.append("Mixed or neutral sentiment")
            
            # Key sentiment drivers
            if abs(sentiment_data.fear_greed_index) > 0.4:
                if sentiment_data.fear_greed_index > 0:
                    reasoning_parts.append("Fear & Greed shows greed dominance")
                else:
                    reasoning_parts.append("Fear & Greed shows fear dominance")
            
            if abs(sentiment_data.social_sentiment) > 0.3:
                if sentiment_data.social_sentiment > 0:
                    reasoning_parts.append("Positive social media sentiment")
                else:
                    reasoning_parts.append("Negative social media sentiment")
            
            # Confidence assessment
            if confidence > 0.8:
                reasoning_parts.append("High confidence due to source agreement")
            elif confidence < 0.5:
                reasoning_parts.append("Lower confidence due to mixed signals")
            
            # Action justification
            if action == 'buy':
                reasoning_parts.append("Sentiment supports buying opportunity")
            elif action == 'sell':
                reasoning_parts.append("Sentiment indicates selling pressure")
            else:
                reasoning_parts.append("Sentiment suggests waiting for clearer signals")
            
            return "; ".join(reasoning_parts)
            
        except Exception as e:
            self.logger.error(f"Error generating sentiment reasoning: {e}")
            return f"Sentiment-based {action} signal with {confidence:.1%} confidence"
    
    def get_sentiment_summary(self) -> Dict[str, Any]:
        """Get current sentiment analysis summary"""
        try:
            recent_sentiments = list(self.sentiment_history)[-10:]
            
            if not recent_sentiments:
                return {'status': 'no_data'}
            
            avg_sentiment = np.mean([s.overall_sentiment for s in recent_sentiments])
            avg_confidence = np.mean([s.confidence for s in recent_sentiments])
            
            return {
                'current_sentiment': recent_sentiments[-1].overall_sentiment if recent_sentiments else 0.0,
                'average_sentiment': avg_sentiment,
                'average_confidence': avg_confidence,
                'quantum_state': dict(self.quantum_state.states),
                'measurements': self.quantum_state.measurement_count,
                'last_collapse': self.quantum_state.last_collapse.isoformat() if self.quantum_state.last_collapse else None,
                'analysis_count': len(self.sentiment_history)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting sentiment summary: {e}")
            return {'status': 'error', 'error': str(e)}
        
        # Performance tracking
        self.stats = {
            'total_analyses': 0,
            'avg_processing_time': 0.0,
            'accuracy_score': 0.0,
            'last_update': datetime.now()
        }
        
    def _setup_analyzers(self):
        """Initialize sentiment analysis tools"""
        try:
            self.vader = VaderSentiment()
            self.tfidf = TfidfVectorizer(max_features=5000, stop_words='english')
            self.classifier = RandomForestClassifier(n_estimators=100, random_state=42)
            
            # Pre-trained transformer model
            if torch.cuda.is_available():
                self.device = torch.device('cuda')
            else:
                self.device = torch.device('cpu')
                
            self.logger.info(f"Sentiment analyzers initialized on device: {self.device}")
            
        except Exception as e:
            self.logger.error(f"Error setting up analyzers: {e}")
            raise
            
    def _setup_data_sources(self):
        """Setup data sources for sentiment analysis"""
        self.news_sources = [
            'https://feeds.finance.yahoo.com/rss/2.0/headline',
            'https://www.coindesk.com/arc/outboundfeeds/rss/',
            'https://cointelegraph.com/rss',
            'https://bitcoinmagazine.com/.rss/full/',
        ]
        
        # Social media APIs (placeholder for real implementations)
        self.social_sources = {
            'reddit': 'r/CryptoCurrency',
            'twitter': '@crypto',
        }
        
        # Market data sources
        self.market_sources = {
            'fear_greed_index': 'https://api.alternative.me/fng/',
            'crypto_fear_greed': 'https://api.alternative.me/fng/?limit=30'
        }
        
    def _setup_quantum_processor(self):
        """Initialize quantum-inspired processing components"""
        # Quantum state vectors for different sentiment dimensions
        self.quantum_dimensions = ['bullish', 'bearish', 'neutral', 'uncertain']
        
        # Initialize superposition states
        self.quantum_state.states = {
            dim: 1.0 / len(self.quantum_dimensions) 
            for dim in self.quantum_dimensions
        }
        
        # Entanglement matrix (correlation between sentiment dimensions)
        self.quantum_state.entanglement_matrix = np.array([
            [1.0, -0.8, -0.1, 0.3],   # Bullish entanglements
            [-0.8, 1.0, -0.1, 0.3],   # Bearish entanglements
            [-0.1, -0.1, 1.0, 0.5],   # Neutral entanglements
            [0.3, 0.3, 0.5, 1.0]      # Uncertain entanglements
        ])
        
    def _setup_neural_models(self):
        """Initialize neural sentiment models"""
        try:
            # Quantum-enhanced neural processor
            self.neural_processor = QuantumNeuralSentimentProcessor()
            
            if torch.cuda.is_available():
                self.neural_processor = self.neural_processor.to(self.device)
                
            # Load pre-trained weights if available
            self._load_pretrained_weights()
            
            self.logger.info("Neural sentiment models initialized")
            
        except Exception as e:
            self.logger.warning(f"Neural models initialization failed: {e}")
            self.neural_processor = None
            
    def _load_pretrained_weights(self):
        """Load pre-trained model weights if available"""
        try:
            # This would load actual pre-trained weights in a real implementation
            # For now, we'll use random initialization
            pass
        except Exception as e:
            self.logger.debug(f"No pre-trained weights found: {e}")
            
    async def analyze_comprehensive_sentiment(
        self, 
        symbol: str = "BTC",
        timeframe: str = "1h",
        sources: List[str] = None
    ) -> SentimentScore:
        """
        Comprehensive sentiment analysis combining multiple sources and quantum processing
        """
        start_time = datetime.now()
        
        try:
            # Gather data from multiple sources
            news_sentiment = await self._analyze_news_sentiment(symbol)
            social_sentiment = await self._analyze_social_sentiment(symbol)
            technical_sentiment = await self._analyze_technical_sentiment(symbol)
            market_sentiment = await self._analyze_market_sentiment()
            
            # Quantum superposition processing
            quantum_sentiment = self._process_quantum_superposition([
                news_sentiment, social_sentiment, technical_sentiment, market_sentiment
            ])
            
            # Neural network enhancement
            if self.neural_processor:
                neural_enhancement = await self._neural_sentiment_enhancement(
                    [news_sentiment, social_sentiment, technical_sentiment, market_sentiment]
                )
                quantum_sentiment = self._combine_quantum_neural(quantum_sentiment, neural_enhancement)
            
            # Calculate final sentiment score
            final_score = self._calculate_final_sentiment(
                quantum_sentiment, symbol, timeframe
            )
            
            # Update quantum state
            self._update_quantum_state(final_score)
            
            # Store in history
            self.sentiment_history.append(final_score)
            
            # Update performance stats
            processing_time = (datetime.now() - start_time).total_seconds()
            self._update_stats(processing_time)
            
            final_score.processing_time = processing_time
            final_score.sources_count = 4  # news, social, technical, market
            
            self.logger.info(
                f"Sentiment analysis complete for {symbol}: "
                f"Bullish={final_score.bullish:.3f}, "
                f"Bearish={final_score.bearish:.3f}, "
                f"Confidence={final_score.confidence:.3f}"
            )
            
            return final_score
            
        except Exception as e:
            self.logger.error(f"Error in comprehensive sentiment analysis: {e}")
            return self._create_default_sentiment()
            
    async def _analyze_news_sentiment(self, symbol: str) -> Dict[str, float]:
        """Analyze sentiment from news sources"""
        try:
            news_data = []
            
            # Fetch news from multiple sources
            async with aiohttp.ClientSession() as session:
                tasks = []
                for source in self.news_sources:
                    tasks.append(self._fetch_news_source(session, source, symbol))
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                for result in results:
                    if isinstance(result, list):
                        news_data.extend(result)
            
            if not news_data:
                return {'bullish': 0.5, 'bearish': 0.5, 'neutral': 0.5, 'confidence': 0.1}
            
            # Process news sentiment
            sentiments = []
            for article in news_data:
                sentiment = self._analyze_text_sentiment(article.get('content', ''))
                sentiments.append(sentiment)
            
            # Aggregate sentiments
            avg_sentiment = self._aggregate_sentiments(sentiments)
            return avg_sentiment
            
        except Exception as e:
            self.logger.error(f"News sentiment analysis error: {e}")
            return {'bullish': 0.5, 'bearish': 0.5, 'neutral': 0.5, 'confidence': 0.1}
            
    async def _fetch_news_source(self, session: aiohttp.ClientSession, source: str, symbol: str) -> List[Dict]:
        """Fetch news from a specific source"""
        try:
            async with session.get(source, timeout=10) as response:
                if response.status == 200:
                    content = await response.text()
                    
                    # Parse RSS feed
                    feed = feedparser.parse(content)
                    
                    articles = []
                    for entry in feed.entries[:10]:  # Limit to recent articles
                        if symbol.lower() in entry.title.lower() or symbol.lower() in entry.summary.lower():
                            articles.append({
                                'title': entry.title,
                                'content': entry.summary,
                                'published': entry.published if hasattr(entry, 'published') else '',
                                'link': entry.link
                            })
                    
                    return articles
                    
        except Exception as e:
            self.logger.debug(f"Error fetching from {source}: {e}")
            return []
            
    async def _analyze_social_sentiment(self, symbol: str) -> Dict[str, float]:
        """Analyze sentiment from social media sources"""
        try:
            # This would implement real social media API calls
            # For now, we'll simulate social sentiment
            
            # Simulate Reddit sentiment
            reddit_sentiment = {
                'bullish': random.uniform(0.2, 0.8),
                'bearish': random.uniform(0.2, 0.8),
                'neutral': random.uniform(0.3, 0.7),
                'confidence': random.uniform(0.5, 0.9)
            }
            
            # Simulate Twitter sentiment
            twitter_sentiment = {
                'bullish': random.uniform(0.2, 0.8),
                'bearish': random.uniform(0.2, 0.8),
                'neutral': random.uniform(0.3, 0.7),
                'confidence': random.uniform(0.5, 0.9)
            }
            
            # Combine social sentiments
            combined = self._weighted_sentiment_combination([
                (reddit_sentiment, 0.6),
                (twitter_sentiment, 0.4)
            ])
            
            return combined
            
        except Exception as e:
            self.logger.error(f"Social sentiment analysis error: {e}")
            return {'bullish': 0.5, 'bearish': 0.5, 'neutral': 0.5, 'confidence': 0.1}
            
    async def _analyze_technical_sentiment(self, symbol: str) -> Dict[str, float]:
        """Analyze technical indicators for sentiment"""
        try:
            # This would analyze actual technical indicators
            # For now, we'll simulate technical sentiment based on common patterns
            
            # Simulate technical analysis results
            rsi_sentiment = random.uniform(0.2, 0.8)  # RSI-based sentiment
            ma_sentiment = random.uniform(0.2, 0.8)   # Moving average sentiment
            macd_sentiment = random.uniform(0.2, 0.8) # MACD sentiment
            volume_sentiment = random.uniform(0.2, 0.8) # Volume sentiment
            
            # Combine technical sentiments
            technical_score = (rsi_sentiment + ma_sentiment + macd_sentiment + volume_sentiment) / 4
            
            if technical_score > 0.6:
                return {'bullish': 0.7, 'bearish': 0.2, 'neutral': 0.1, 'confidence': 0.8}
            elif technical_score < 0.4:
                return {'bullish': 0.2, 'bearish': 0.7, 'neutral': 0.1, 'confidence': 0.8}
            else:
                return {'bullish': 0.4, 'bearish': 0.4, 'neutral': 0.2, 'confidence': 0.6}
                
        except Exception as e:
            self.logger.error(f"Technical sentiment analysis error: {e}")
            return {'bullish': 0.5, 'bearish': 0.5, 'neutral': 0.0, 'confidence': 0.1}
            
    async def _analyze_market_sentiment(self) -> Dict[str, float]:
        """Analyze overall market sentiment indicators"""
        try:
            # Fetch Fear & Greed Index
            async with aiohttp.ClientSession() as session:
                try:
                    async with session.get(self.market_sources['fear_greed_index'], timeout=10) as response:
                        if response.status == 200:
                            data = await response.json()
                            fear_greed_value = int(data['data'][0]['value'])
                            
                            # Convert Fear & Greed to sentiment
                            if fear_greed_value > 70:  # Extreme Greed
                                return {'bullish': 0.8, 'bearish': 0.1, 'neutral': 0.1, 'confidence': 0.9}
                            elif fear_greed_value > 50:  # Greed
                                return {'bullish': 0.6, 'bearish': 0.2, 'neutral': 0.2, 'confidence': 0.7}
                            elif fear_greed_value > 30:  # Neutral
                                return {'bullish': 0.4, 'bearish': 0.4, 'neutral': 0.2, 'confidence': 0.5}
                            elif fear_greed_value > 10:  # Fear
                                return {'bullish': 0.2, 'bearish': 0.6, 'neutral': 0.2, 'confidence': 0.7}
                            else:  # Extreme Fear
                                return {'bullish': 0.1, 'bearish': 0.8, 'neutral': 0.1, 'confidence': 0.9}
                                
                except Exception as e:
                    self.logger.debug(f"Fear & Greed index fetch error: {e}")
            
            # Default market sentiment
            return {'bullish': 0.5, 'bearish': 0.5, 'neutral': 0.0, 'confidence': 0.3}
            
        except Exception as e:
            self.logger.error(f"Market sentiment analysis error: {e}")
            return {'bullish': 0.5, 'bearish': 0.5, 'neutral': 0.0, 'confidence': 0.1}
            
    def _analyze_text_sentiment(self, text: str) -> Dict[str, float]:
        """Analyze sentiment of individual text using multiple methods"""
        try:
            if not text or len(text.strip()) < 5:
                return {'bullish': 0.5, 'bearish': 0.5, 'neutral': 0.5, 'confidence': 0.1}
            
            # TextBlob analysis
            blob = TextBlob(text)
            polarity = blob.sentiment.polarity
            subjectivity = blob.sentiment.subjectivity
            
            # VADER analysis
            vader_scores = self.vader.polarity_scores(text)
            
            # Combine scores
            if polarity > 0.1 and vader_scores['compound'] > 0.1:
                return {
                    'bullish': min(0.9, 0.5 + polarity * 0.5 + vader_scores['pos'] * 0.3),
                    'bearish': max(0.1, 0.5 - polarity * 0.5 - vader_scores['pos'] * 0.3),
                    'neutral': vader_scores['neu'],
                    'confidence': min(0.9, subjectivity * 0.7 + abs(vader_scores['compound']) * 0.3)
                }
            elif polarity < -0.1 and vader_scores['compound'] < -0.1:
                return {
                    'bullish': max(0.1, 0.5 + polarity * 0.5),
                    'bearish': min(0.9, 0.5 - polarity * 0.5 + vader_scores['neg'] * 0.3),
                    'neutral': vader_scores['neu'],
                    'confidence': min(0.9, subjectivity * 0.7 + abs(vader_scores['compound']) * 0.3)
                }
            else:
                return {
                    'bullish': 0.5 + polarity * 0.2,
                    'bearish': 0.5 - polarity * 0.2,
                    'neutral': max(0.3, vader_scores['neu']),
                    'confidence': min(0.6, subjectivity * 0.5 + abs(vader_scores['compound']) * 0.2)
                }
                
        except Exception as e:
            self.logger.debug(f"Text sentiment analysis error: {e}")
            return {'bullish': 0.5, 'bearish': 0.5, 'neutral': 0.5, 'confidence': 0.1}
            
    def _process_quantum_superposition(self, sentiments: List[Dict[str, float]]) -> Dict[str, float]:
        """Process sentiments using quantum superposition principles"""
        try:
            if not sentiments:
                return {'bullish': 0.5, 'bearish': 0.5, 'neutral': 0.5, 'uncertainty': 0.5}
            
            # Initialize quantum state vectors
            quantum_states = np.zeros((len(sentiments), len(self.quantum_dimensions)))
            
            for i, sentiment in enumerate(sentiments):
                quantum_states[i] = [
                    sentiment.get('bullish', 0.5),
                    sentiment.get('bearish', 0.5),
                    sentiment.get('neutral', 0.5),
                    sentiment.get('confidence', 0.5)
                ]
            
            # Apply quantum entanglement (correlation matrix)
            entangled_states = np.dot(quantum_states, self.quantum_state.entanglement_matrix)
            
            # Calculate superposition
            superposition = np.mean(entangled_states, axis=0)
            
            # Apply quantum uncertainty principle
            uncertainty = np.std(entangled_states, axis=0).mean()
            
            # Normalize probabilities
            total = superposition[:3].sum()
            if total > 0:
                superposition[:3] /= total
            
            return {
                'bullish': float(superposition[0]),
                'bearish': float(superposition[1]),
                'neutral': float(superposition[2]),
                'uncertainty': float(uncertainty),
                'coherence': float(1.0 - uncertainty)  # Quantum coherence measure
            }
            
        except Exception as e:
            self.logger.error(f"Quantum superposition processing error: {e}")
            return {'bullish': 0.5, 'bearish': 0.5, 'neutral': 0.5, 'uncertainty': 0.5}
            
    async def _neural_sentiment_enhancement(self, sentiments: List[Dict[str, float]]) -> Dict[str, float]:
        """Enhance sentiment analysis using neural networks"""
        try:
            if not self.neural_processor or not sentiments:
                return {'bullish': 0.5, 'bearish': 0.5, 'neutral': 0.5, 'confidence': 0.5}
            
            # Prepare input tensor
            input_features = []
            for sentiment in sentiments:
                features = [
                    sentiment.get('bullish', 0.5),
                    sentiment.get('bearish', 0.5),
                    sentiment.get('neutral', 0.5),
                    sentiment.get('confidence', 0.5)
                ]
                input_features.extend(features)
            
            # Pad or truncate to expected input size
            while len(input_features) < 768:
                input_features.extend([0.0] * min(4, 768 - len(input_features)))
            input_features = input_features[:768]
            
            # Convert to tensor
            input_tensor = torch.FloatTensor(input_features).unsqueeze(0).unsqueeze(0)
            
            if torch.cuda.is_available():
                input_tensor = input_tensor.to(self.device)
            
            # Neural processing
            with torch.no_grad():
                sentiment_logits, uncertainty = self.neural_processor(input_tensor)
                
                sentiment_probs = sentiment_logits.cpu().numpy()[0]
                uncertainty_score = uncertainty.cpu().numpy()[0][0]
            
            return {
                'bullish': float(sentiment_probs[0]),
                'bearish': float(sentiment_probs[1]),
                'neutral': float(sentiment_probs[2]),
                'confidence': float(1.0 - uncertainty_score)
            }
            
        except Exception as e:
            self.logger.error(f"Neural sentiment enhancement error: {e}")
            return {'bullish': 0.5, 'bearish': 0.5, 'neutral': 0.5, 'confidence': 0.5}
            
    def _combine_quantum_neural(self, quantum: Dict[str, float], neural: Dict[str, float]) -> Dict[str, float]:
        """Combine quantum and neural sentiment processing results"""
        try:
            # Weighted combination (quantum processing gets higher weight for uncertainty handling)
            quantum_weight = 0.7
            neural_weight = 0.3
            
            combined = {
                'bullish': quantum['bullish'] * quantum_weight + neural['bullish'] * neural_weight,
                'bearish': quantum['bearish'] * quantum_weight + neural['bearish'] * neural_weight,
                'neutral': quantum['neutral'] * quantum_weight + neural['neutral'] * neural_weight,
                'uncertainty': quantum.get('uncertainty', 0.5),
                'coherence': quantum.get('coherence', 0.5),
                'confidence': (quantum.get('confidence', 0.5) + neural.get('confidence', 0.5)) / 2
            }
            
            # Ensure probabilities sum to 1
            total = combined['bullish'] + combined['bearish'] + combined['neutral']
            if total > 0:
                combined['bullish'] /= total
                combined['bearish'] /= total
                combined['neutral'] /= total
            
            return combined
            
        except Exception as e:
            self.logger.error(f"Quantum-neural combination error: {e}")
            return quantum  # Fallback to quantum only
            
    def _calculate_final_sentiment(
        self, 
        quantum_sentiment: Dict[str, float], 
        symbol: str, 
        timeframe: str
    ) -> SentimentScore:
        """Calculate final comprehensive sentiment score"""
        try:
            # Create base sentiment score
            score = SentimentScore(
                bullish=quantum_sentiment.get('bullish', 0.5),
                bearish=quantum_sentiment.get('bearish', 0.5),
                neutral=quantum_sentiment.get('neutral', 0.5),
                uncertainty=quantum_sentiment.get('uncertainty', 0.5),
                confidence=quantum_sentiment.get('confidence', 0.5),
                quantum_coherence=quantum_sentiment.get('coherence', 0.5)
            )
            
            # Calculate magnitude
            score.magnitude = abs(score.bullish - score.bearish)
            
            # Add temporal components based on timeframe
            if timeframe == "5m":
                score.short_term = score.bullish * 0.8 + score.bearish * 0.2
                score.medium_term = score.bullish * 0.6 + score.bearish * 0.4
                score.long_term = score.bullish * 0.4 + score.bearish * 0.6
            elif timeframe == "1h":
                score.short_term = score.bullish * 0.6 + score.bearish * 0.4
                score.medium_term = score.bullish * 0.7 + score.bearish * 0.3
                score.long_term = score.bullish * 0.5 + score.bearish * 0.5
            else:  # 1d or longer
                score.short_term = score.bullish * 0.4 + score.bearish * 0.6
                score.medium_term = score.bullish * 0.5 + score.bearish * 0.5
                score.long_term = score.bullish * 0.8 + score.bearish * 0.2
            
            # Add market-specific adjustments
            if symbol.upper() in ['BTC', 'ETH']:  # Major cryptos
                score.confidence *= 1.1  # Higher confidence for major assets
                score.institutional_sentiment = score.bullish * 0.7 + score.neutral * 0.3
            else:  # Altcoins
                score.confidence *= 0.9  # Lower confidence for smaller assets
                score.institutional_sentiment = score.neutral * 0.6 + score.bullish * 0.4
            
            # Add source weights
            score.news_weight = self.source_weights['news']
            score.social_weight = self.source_weights['social']
            score.technical_weight = self.source_weights['technical']
            score.fundamental_weight = self.source_weights['fundamental']
            
            # Calculate momentum based on recent history
            if len(self.sentiment_history) > 5:
                recent_scores = list(self.sentiment_history)[-5:]
                momentum = sum(s.bullish - s.bearish for s in recent_scores) / len(recent_scores)
                score.momentum = momentum
            
            # Volatility sentiment
            score.volatility_sentiment = min(1.0, score.uncertainty * 2.0)
            
            # Market fear/greed (inverse of uncertainty)
            score.market_fear_greed = 1.0 - score.uncertainty
            
            # Ensure all values are within bounds
            score = self._normalize_sentiment_score(score)
            
            return score
            
        except Exception as e:
            self.logger.error(f"Final sentiment calculation error: {e}")
            return self._create_default_sentiment()
            
    def _normalize_sentiment_score(self, score: SentimentScore) -> SentimentScore:
        """Normalize all sentiment score values to valid ranges"""
        try:
            # Normalize main sentiment probabilities
            total = score.bullish + score.bearish + score.neutral
            if total > 0:
                score.bullish /= total
                score.bearish /= total
                score.neutral /= total
            
            # Clamp all values to [0, 1]
            for field in score.__dataclass_fields__:
                if field in ['timestamp', 'sources_count', 'processing_time']:
                    continue
                    
                value = getattr(score, field)
                if isinstance(value, (int, float)):
                    setattr(score, field, max(0.0, min(1.0, float(value))))
            
            return score
            
        except Exception as e:
            self.logger.error(f"Normalization error: {e}")
            return score
            
    def _aggregate_sentiments(self, sentiments: List[Dict[str, float]]) -> Dict[str, float]:
        """Aggregate multiple sentiment scores"""
        if not sentiments:
            return {'bullish': 0.5, 'bearish': 0.5, 'neutral': 0.5, 'confidence': 0.1}
        
        # Calculate weighted average
        total_weight = sum(s.get('confidence', 0.5) for s in sentiments)
        
        if total_weight == 0:
            total_weight = len(sentiments)
            weights = [1.0] * len(sentiments)
        else:
            weights = [s.get('confidence', 0.5) for s in sentiments]
        
        aggregated = {
            'bullish': sum(s.get('bullish', 0.5) * w for s, w in zip(sentiments, weights)) / total_weight,
            'bearish': sum(s.get('bearish', 0.5) * w for s, w in zip(sentiments, weights)) / total_weight,
            'neutral': sum(s.get('neutral', 0.5) * w for s, w in zip(sentiments, weights)) / total_weight,
            'confidence': sum(weights) / len(weights)
        }
        
        return aggregated
        
    def _weighted_sentiment_combination(self, weighted_sentiments: List[Tuple[Dict, float]]) -> Dict[str, float]:
        """Combine sentiments with specific weights"""
        if not weighted_sentiments:
            return {'bullish': 0.5, 'bearish': 0.5, 'neutral': 0.5, 'confidence': 0.1}
        
        total_weight = sum(weight for _, weight in weighted_sentiments)
        
        combined = {
            'bullish': sum(sentiment.get('bullish', 0.5) * weight for sentiment, weight in weighted_sentiments) / total_weight,
            'bearish': sum(sentiment.get('bearish', 0.5) * weight for sentiment, weight in weighted_sentiments) / total_weight,
            'neutral': sum(sentiment.get('neutral', 0.5) * weight for sentiment, weight in weighted_sentiments) / total_weight,
            'confidence': sum(sentiment.get('confidence', 0.5) * weight for sentiment, weight in weighted_sentiments) / total_weight
        }
        
        return combined
        
    def _update_quantum_state(self, sentiment_score: SentimentScore):
        """Update quantum state based on new sentiment measurement"""
        try:
            # Quantum state collapse simulation
            self.quantum_state.measurement_count += 1
            self.quantum_state.last_collapse = datetime.now()
            
            # Update state probabilities
            self.quantum_state.states = {
                'bullish': sentiment_score.bullish,
                'bearish': sentiment_score.bearish,
                'neutral': sentiment_score.neutral,
                'uncertain': sentiment_score.uncertainty
            }
            
            # Simulate decoherence over time
            time_since_last = (datetime.now() - (self.quantum_state.last_collapse or datetime.now())).total_seconds()
            decoherence_factor = min(1.0, time_since_last / self.quantum_state.decoherence_time)
            
            # Apply decoherence to entanglement matrix
            identity = np.eye(len(self.quantum_dimensions))
            self.quantum_state.entanglement_matrix = (
                self.quantum_state.entanglement_matrix * (1 - decoherence_factor) + 
                identity * decoherence_factor
            )
            
        except Exception as e:
            self.logger.error(f"Quantum state update error: {e}")
            
    def _update_stats(self, processing_time: float):
        """Update performance statistics"""
        try:
            self.stats['total_analyses'] += 1
            
            # Update average processing time
            current_avg = self.stats['avg_processing_time']
            count = self.stats['total_analyses']
            self.stats['avg_processing_time'] = (current_avg * (count - 1) + processing_time) / count
            
            self.stats['last_update'] = datetime.now()
            
        except Exception as e:
            self.logger.error(f"Stats update error: {e}")
            
    def _create_default_sentiment(self) -> SentimentScore:
        """Create a default neutral sentiment score"""
        return SentimentScore(
            bullish=0.33,
            bearish=0.33,
            neutral=0.34,
            uncertainty=0.5,
            confidence=0.1,
            magnitude=0.0,
            quantum_coherence=0.5
        )
        
    async def get_sentiment_trend(self, symbol: str, periods: int = 24) -> List[SentimentScore]:
        """Get sentiment trend over multiple periods"""
        try:
            trends = []
            
            for i in range(periods):
                # This would fetch historical sentiment data in a real implementation
                # For now, we'll create simulated trend data
                timestamp = datetime.now() - timedelta(hours=periods - i)
                
                sentiment = await self.analyze_comprehensive_sentiment(symbol)
                sentiment.timestamp = timestamp
                
                trends.append(sentiment)
                
                # Small delay to prevent overwhelming APIs
                await asyncio.sleep(0.1)
            
            return trends
            
        except Exception as e:
            self.logger.error(f"Sentiment trend analysis error: {e}")
            return []
            
    def get_quantum_state_info(self) -> Dict[str, Any]:
        """Get current quantum state information"""
        return {
            'states': self.quantum_state.states,
            'entanglement_matrix': self.quantum_state.entanglement_matrix.tolist(),
            'measurement_count': self.quantum_state.measurement_count,
            'last_collapse': self.quantum_state.last_collapse,
            'decoherence_time': self.quantum_state.decoherence_time
        }
        
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        return self.stats.copy()
        
    async def shutdown(self):
        """Cleanup resources"""
        try:
            self.logger.info("Shutting down SentimentAnalyzer")
            
            # Clear neural model from memory
            if self.neural_processor and torch.cuda.is_available():
                self.neural_processor = self.neural_processor.cpu()
                torch.cuda.empty_cache()
            
            # Clear history to free memory
            self.sentiment_history.clear()
            
            self.logger.info("SentimentAnalyzer shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Shutdown error: {e}")


# Enhanced Sentiment Manager for integration with other systems
class EnhancedSentimentManager:
    """
    Enhanced Sentiment Manager for system-wide sentiment analysis coordination
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.analyzer = SentimentAnalyzer(config)
        self.logger = logging.getLogger(__name__ + ".EnhancedSentimentManager")
        
        # Cache for recent sentiment analyses
        self.sentiment_cache = {}
        self.cache_ttl = 300  # 5 minutes
        
    async def get_market_sentiment(self, symbol: str = "BTC") -> SentimentScore:
        """Get comprehensive market sentiment with caching"""
        cache_key = f"{symbol}_market_sentiment"
        
        # Check cache
        if cache_key in self.sentiment_cache:
            cached_result, timestamp = self.sentiment_cache[cache_key]
            if (datetime.now() - timestamp).total_seconds() < self.cache_ttl:
                return cached_result
        
        # Analyze sentiment
        sentiment = await self.analyzer.analyze_comprehensive_sentiment(symbol)
        
        # Cache result
        self.sentiment_cache[cache_key] = (sentiment, datetime.now())
        
        return sentiment
        
    async def get_trading_sentiment_signal(self, symbol: str, timeframe: str = "1h") -> Dict[str, Any]:
        """Get sentiment signal for trading decisions"""
        sentiment = await self.get_market_sentiment(symbol)
        
        # Generate trading signal based on sentiment
        if sentiment.bullish > 0.7 and sentiment.confidence > 0.6:
            signal = "STRONG_BUY"
        elif sentiment.bullish > 0.6 and sentiment.confidence > 0.5:
            signal = "BUY"
        elif sentiment.bearish > 0.7 and sentiment.confidence > 0.6:
            signal = "STRONG_SELL"
        elif sentiment.bearish > 0.6 and sentiment.confidence > 0.5:
            signal = "SELL"
        elif sentiment.uncertainty > 0.7:
            signal = "WAIT"
        else:
            signal = "NEUTRAL"
        
        return {
            'signal': signal,
            'sentiment': sentiment,
            'strength': sentiment.magnitude * sentiment.confidence,
            'timeframe': timeframe,
            'timestamp': datetime.now()
        }
        
    async def cleanup(self):
        """Cleanup resources"""
        await self.analyzer.shutdown()
        self.sentiment_cache.clear()


# Factory function for creating sentiment analyzer
def create_sentiment_analyzer(config: Dict[str, Any] = None) -> SentimentAnalyzer:
    """Factory function to create and initialize sentiment analyzer"""
    return SentimentAnalyzer(config)


# Export classes for import
__all__ = [
    'SentimentAnalyzer',
    'EnhancedSentimentManager', 
    'SentimentScore',
    'QuantumSentimentState',
    'create_sentiment_analyzer'
]
