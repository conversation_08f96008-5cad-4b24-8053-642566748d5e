"""
Kraken Exchange Client for Professional Trading
Implements Kraken REST API with advanced features
"""

import asyncio
import logging
import time
import hmac
import hashlib
import base64
import json
import aiohttp
from typing import Dict, List, Optional, Tuple, Any, Union
from decimal import Decimal
from urllib.parse import urlencode
from src.utils.cryptography.secure_credentials import decrypt_value

logger = logging.getLogger(__name__)

class KrakenClient:
    """Professional Kraken client with advanced trading features"""
    
    def __init__(self, api_key: str = None, api_secret: str = None, testnet: bool = False):
        self.api_key = api_key
        self.api_secret = api_secret
        self.testnet = testnet
        
        # API endpoints
        if testnet:
            self.base_url = "https://api.demo.kraken.com"
        else:
            self.base_url = "https://api.kraken.com"
        
        # Rate limiting
        self.api_counter = 0
        self.last_request_time = 0
        self.rate_limit_window = 60  # 1 minute
        self.max_requests_per_minute = 60
        
        # Connection pool
        self.session = None
        self.connector = None
        
        # Market data cache
        self.asset_pairs = {}
        self.price_cache = {}
        self.last_price_update = 0
        
        logger.info(f"🟣 [KRAKEN] Client initialized ({'testnet' if testnet else 'live'})")

    async def initialize(self):
        """Initialize the client with connection pool"""
        try:
            # Create connection pool
            self.connector = aiohttp.TCPConnector(
                limit=100,
                limit_per_host=30,
                ttl_dns_cache=300,
                use_dns_cache=True,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )
            
            self.session = aiohttp.ClientSession(
                connector=self.connector,
                timeout=aiohttp.ClientTimeout(total=30),
                headers={
                    'User-Agent': 'AutoGPT-Trader/1.0'
                }
            )
            
            # Load asset pairs
            await self._load_asset_pairs()
            
            logger.info("✅ [KRAKEN] Client initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ [KRAKEN] Initialization failed: {e}")
            return False

    async def close(self):
        """Close the client and cleanup resources"""
        if self.session:
            await self.session.close()
        if self.connector:
            await self.connector.close()

    def _generate_signature(self, url_path: str, data: str, nonce: str) -> str:
        """Generate signature for authenticated requests"""
        if not self.api_secret:
            raise ValueError("API secret is required for authenticated requests")

        postdata = urlencode(data)
        encoded = (nonce + postdata).encode()
        message = url_path.encode() + hashlib.sha256(encoded).digest()

        mac = hmac.new(base64.b64decode(self.api_secret), message, hashlib.sha512)
        sigdigest = base64.b64encode(mac.digest())
        return sigdigest.decode()

    async def _make_request(self, endpoint: str, params: Dict = None, 
                           private: bool = False) -> Dict:
        """Make API request with rate limiting"""
        if params is None:
            params = {}
        
        # Rate limiting
        await self._check_rate_limit()
        
        # Build URL
        if private:
            url = f"{self.base_url}/0/private/{endpoint}"
        else:
            url = f"{self.base_url}/0/public/{endpoint}"
        
        # Prepare headers and data
        headers = {}
        data = params.copy()
        
        if private:
            if not self.api_key:
                raise ValueError("API key is required for authenticated requests")

            nonce = str(int(time.time() * 1000000))
            data['nonce'] = nonce

            signature = self._generate_signature(f"/0/private/{endpoint}", data, nonce)
            headers.update({
                'API-Key': self.api_key,
                'API-Sign': signature
            })
        
        try:
            if private:
                # POST for private endpoints
                async with self.session.post(url, data=data, headers=headers) as response:
                    result = await response.json()
            else:
                # GET for public endpoints
                async with self.session.get(url, params=params) as response:
                    result = await response.json()
            
            if result.get('error'):
                error_msg = ', '.join(result['error'])
                logger.error(f"❌ [KRAKEN] API error: {error_msg}")
                raise Exception(f"Kraken API error: {error_msg}")
            
            return result.get('result', {})
            
        except Exception as e:
            logger.error(f"❌ [KRAKEN] Request failed: {e}")
            raise

    async def _check_rate_limit(self):
        """Check and enforce rate limits"""
        current_time = time.time()
        
        # Reset counter if window has passed
        if current_time - self.last_request_time >= self.rate_limit_window:
            self.api_counter = 0
            self.last_request_time = current_time
        
        # Check if we're approaching limits
        if self.api_counter >= self.max_requests_per_minute:
            sleep_time = self.rate_limit_window - (current_time - self.last_request_time)
            if sleep_time > 0:
                logger.warning(f"⚠️ [KRAKEN] Rate limit reached, sleeping {sleep_time:.2f}s")
                await asyncio.sleep(sleep_time)
                self.api_counter = 0
                self.last_request_time = time.time()
        
        self.api_counter += 1

    async def _load_asset_pairs(self):
        """Load asset pairs information"""
        try:
            pairs = await self._make_request('AssetPairs')
            
            for pair_name, pair_data in pairs.items():
                self.asset_pairs[pair_name] = {
                    'altname': pair_data.get('altname'),
                    'base': pair_data.get('base'),
                    'quote': pair_data.get('quote'),
                    'lot': pair_data.get('lot'),
                    'pair_decimals': pair_data.get('pair_decimals'),
                    'lot_decimals': pair_data.get('lot_decimals'),
                    'lot_multiplier': pair_data.get('lot_multiplier'),
                    'leverage_buy': pair_data.get('leverage_buy', []),
                    'leverage_sell': pair_data.get('leverage_sell', []),
                    'fees': pair_data.get('fees', []),
                    'fees_maker': pair_data.get('fees_maker', []),
                    'fee_volume_currency': pair_data.get('fee_volume_currency'),
                    'margin_call': pair_data.get('margin_call'),
                    'margin_stop': pair_data.get('margin_stop'),
                    'ordermin': pair_data.get('ordermin')
                }
            
            logger.info(f"✅ [KRAKEN] Loaded {len(self.asset_pairs)} asset pairs")
            
        except Exception as e:
            logger.error(f"❌ [KRAKEN] Failed to load asset pairs: {e}")

    async def get_balance(self, asset: str = None) -> Union[Dict, Decimal]:
        """Get account balance"""
        try:
            balances = await self._make_request('Balance', private=True)
            
            if asset:
                # Kraken uses different asset names (e.g., ZUSD for USD)
                kraken_asset = self._normalize_asset_name(asset)
                return Decimal(balances.get(kraken_asset, '0'))
            else:
                result = {}
                for asset_name, balance in balances.items():
                    balance_decimal = Decimal(balance)
                    if balance_decimal > 0:
                        # Convert back to standard names
                        standard_name = self._denormalize_asset_name(asset_name)
                        result[standard_name] = balance_decimal
                return result
                
        except Exception as e:
            logger.error(f"❌ [KRAKEN] Failed to get balance: {e}")
            return Decimal('0') if asset else {}

    def _normalize_asset_name(self, asset: str) -> str:
        """Convert standard asset names to Kraken format"""
        mapping = {
            'USD': 'ZUSD',
            'EUR': 'ZEUR',
            'BTC': 'XXBT',
            'ETH': 'XETH',
            'LTC': 'XLTC',
            'XRP': 'XXRP'
        }
        return mapping.get(asset, asset)

    def _denormalize_asset_name(self, kraken_asset: str) -> str:
        """Convert Kraken asset names to standard format"""
        mapping = {
            'ZUSD': 'USD',
            'ZEUR': 'EUR',
            'XXBT': 'BTC',
            'XETH': 'ETH',
            'XLTC': 'LTC',
            'XXRP': 'XRP'
        }
        return mapping.get(kraken_asset, kraken_asset)

    async def get_price(self, pair: str) -> Optional[Decimal]:
        """Get current price for trading pair"""
        try:
            # Check cache first
            current_time = time.time()
            if (pair in self.price_cache and 
                current_time - self.last_price_update < 5):  # 5 second cache
                return self.price_cache[pair]
            
            # Fetch from API
            ticker = await self._make_request('Ticker', {'pair': pair})
            
            if pair in ticker:
                price = Decimal(ticker[pair]['c'][0])  # Last trade price
                
                # Update cache
                self.price_cache[pair] = price
                self.last_price_update = current_time
                
                return price
            
            return None
            
        except Exception as e:
            logger.error(f"❌ [KRAKEN] Failed to get price for {pair}: {e}")
            return None

    async def get_order_book(self, pair: str, count: int = 100) -> Dict:
        """Get order book for trading pair"""
        try:
            params = {'pair': pair, 'count': count}
            response = await self._make_request('Depth', params)
            
            if pair in response:
                book_data = response[pair]
                return {
                    'bids': [(Decimal(price), Decimal(volume)) for price, volume, _ in book_data['bids']],
                    'asks': [(Decimal(price), Decimal(volume)) for price, volume, _ in book_data['asks']],
                    'timestamp': int(time.time())
                }
            
            return {'bids': [], 'asks': [], 'timestamp': 0}
            
        except Exception as e:
            logger.error(f"❌ [KRAKEN] Failed to get order book for {pair}: {e}")
            return {'bids': [], 'asks': [], 'timestamp': 0}

    async def place_order(self, pair: str, type_: str, ordertype: str,
                         volume: Decimal, price: Decimal = None, **kwargs) -> Dict:
        """Place a trading order"""
        try:
            params = {
                'pair': pair,
                'type': type_.lower(),  # buy or sell
                'ordertype': ordertype.lower(),  # market, limit, etc.
                'volume': str(volume)
            }

            # Add price for limit orders
            if ordertype.lower() in ['limit', 'stop-loss-limit', 'take-profit-limit']:
                if not price:
                    raise ValueError(f"Price required for {ordertype} orders")
                params['price'] = str(price)

            # Add additional parameters
            for key, value in kwargs.items():
                if value is not None:
                    params[key] = str(value)

            logger.warning(f"💰 [KRAKEN] Placing {type_} {ordertype} order: {pair} {volume} @ {price or 'market'}")

            # Execute order
            response = await self._make_request('AddOrder', params, private=True)

            order_ids = response.get('txid', [])
            if order_ids:
                logger.info(f"✅ [KRAKEN] Order placed successfully: {order_ids[0]}")

                return {
                    'order_id': order_ids[0],
                    'pair': pair,
                    'type': type_,
                    'ordertype': ordertype,
                    'volume': volume,
                    'price': price,
                    'status': 'pending',
                    'timestamp': int(time.time() * 1000),
                    'description': response.get('descr', {})
                }
            else:
                raise Exception("No order ID returned")

        except Exception as e:
            logger.error(f"❌ [KRAKEN] Failed to place order: {e}")
            raise

    async def cancel_order(self, order_id: str) -> Dict:
        """Cancel an existing order"""
        try:
            params = {'txid': order_id}
            response = await self._make_request('CancelOrder', params, private=True)

            logger.info(f"✅ [KRAKEN] Order {order_id} cancelled successfully")

            return {
                'order_id': order_id,
                'status': 'cancelled',
                'count': response.get('count', 0)
            }

        except Exception as e:
            logger.error(f"❌ [KRAKEN] Failed to cancel order {order_id}: {e}")
            raise

    async def get_order_status(self, order_id: str) -> Dict:
        """Get order status"""
        try:
            params = {'txid': order_id}
            response = await self._make_request('QueryOrders', params, private=True)

            if order_id in response:
                order_data = response[order_id]

                return {
                    'order_id': order_id,
                    'pair': order_data.get('descr', {}).get('pair'),
                    'type': order_data.get('descr', {}).get('type'),
                    'ordertype': order_data.get('descr', {}).get('ordertype'),
                    'volume': Decimal(order_data.get('vol', '0')),
                    'price': Decimal(order_data.get('descr', {}).get('price', '0')),
                    'executed_volume': Decimal(order_data.get('vol_exec', '0')),
                    'cost': Decimal(order_data.get('cost', '0')),
                    'fee': Decimal(order_data.get('fee', '0')),
                    'status': order_data.get('status'),
                    'timestamp': order_data.get('opentm'),
                    'close_timestamp': order_data.get('closetm')
                }
            else:
                raise Exception(f"Order {order_id} not found")

        except Exception as e:
            logger.error(f"❌ [KRAKEN] Failed to get order status: {e}")
            raise

    async def get_open_orders(self) -> List[Dict]:
        """Get all open orders"""
        try:
            response = await self._make_request('OpenOrders', private=True)

            orders = []
            open_orders = response.get('open', {})

            for order_id, order_data in open_orders.items():
                orders.append({
                    'order_id': order_id,
                    'pair': order_data.get('descr', {}).get('pair'),
                    'type': order_data.get('descr', {}).get('type'),
                    'ordertype': order_data.get('descr', {}).get('ordertype'),
                    'volume': Decimal(order_data.get('vol', '0')),
                    'price': Decimal(order_data.get('descr', {}).get('price', '0')),
                    'executed_volume': Decimal(order_data.get('vol_exec', '0')),
                    'cost': Decimal(order_data.get('cost', '0')),
                    'fee': Decimal(order_data.get('fee', '0')),
                    'status': order_data.get('status'),
                    'timestamp': order_data.get('opentm')
                })

            return orders

        except Exception as e:
            logger.error(f"❌ [KRAKEN] Failed to get open orders: {e}")
            return []

    async def get_trade_history(self, pair: str = None, start: int = None,
                               end: int = None) -> List[Dict]:
        """Get trade history"""
        try:
            params = {}
            if pair:
                params['pair'] = pair
            if start:
                params['start'] = start
            if end:
                params['end'] = end

            response = await self._make_request('TradesHistory', params, private=True)

            trades = []
            trades_data = response.get('trades', {})

            for trade_id, trade_data in trades_data.items():
                trades.append({
                    'trade_id': trade_id,
                    'order_id': trade_data.get('ordertxid'),
                    'pair': trade_data.get('pair'),
                    'type': trade_data.get('type'),
                    'ordertype': trade_data.get('ordertype'),
                    'volume': Decimal(trade_data.get('vol', '0')),
                    'price': Decimal(trade_data.get('price', '0')),
                    'cost': Decimal(trade_data.get('cost', '0')),
                    'fee': Decimal(trade_data.get('fee', '0')),
                    'margin': Decimal(trade_data.get('margin', '0')),
                    'timestamp': trade_data.get('time')
                })

            return trades

        except Exception as e:
            logger.error(f"❌ [KRAKEN] Failed to get trade history: {e}")
            return []

    async def get_ticker_info(self, pair: str) -> Dict:
        """Get detailed ticker information"""
        try:
            ticker = await self._make_request('Ticker', {'pair': pair})

            if pair in ticker:
                data = ticker[pair]
                return {
                    'pair': pair,
                    'ask': Decimal(data['a'][0]),  # Ask price
                    'ask_volume': Decimal(data['a'][2]),  # Ask volume
                    'bid': Decimal(data['b'][0]),  # Bid price
                    'bid_volume': Decimal(data['b'][2]),  # Bid volume
                    'last_price': Decimal(data['c'][0]),  # Last trade price
                    'last_volume': Decimal(data['c'][1]),  # Last trade volume
                    'volume_today': Decimal(data['v'][0]),  # Volume today
                    'volume_24h': Decimal(data['v'][1]),  # Volume 24h
                    'vwap_today': Decimal(data['p'][0]),  # VWAP today
                    'vwap_24h': Decimal(data['p'][1]),  # VWAP 24h
                    'trades_today': int(data['t'][0]),  # Trades today
                    'trades_24h': int(data['t'][1]),  # Trades 24h
                    'low_today': Decimal(data['l'][0]),  # Low today
                    'low_24h': Decimal(data['l'][1]),  # Low 24h
                    'high_today': Decimal(data['h'][0]),  # High today
                    'high_24h': Decimal(data['h'][1]),  # High 24h
                    'open_price': Decimal(data['o'])  # Open price
                }

            return {}

        except Exception as e:
            logger.error(f"❌ [KRAKEN] Failed to get ticker info for {pair}: {e}")
            return {}

    def get_supported_pairs(self) -> List[str]:
        """Get list of supported trading pairs"""
        return list(self.asset_pairs.keys())

    def get_pair_info(self, pair: str) -> Dict:
        """Get detailed information about a trading pair"""
        return self.asset_pairs.get(pair, {})
