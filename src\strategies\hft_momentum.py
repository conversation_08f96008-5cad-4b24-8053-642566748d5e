"""
High-Frequency Trading Momentum Strategy
Advanced momentum-based strategy for ultra-fast market execution
"""

import asyncio
import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from collections import deque
from decimal import Decimal

from .base import BaseStrategy, SignalData, TradeSignal, MarketContext

logger = logging.getLogger(__name__)

@dataclass
class MomentumFeatures:
    """Features for momentum analysis"""
    price_momentum_5s: float = 0.0
    price_momentum_15s: float = 0.0
    price_momentum_1m: float = 0.0
    volume_momentum: float = 0.0
    spread_momentum: float = 0.0
    order_book_imbalance: float = 0.0
    volatility_regime: str = "normal"
    liquidity_score: float = 0.0
    microstructure_score: float = 0.0
    regime_confidence: float = 0.0

class HFTMomentumStrategy(BaseStrategy):
    """
    High-frequency momentum strategy with microsecond precision timing
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("HFTMomentumStrategy", config or {})
        
        # HFT-specific parameters
        self.alpha = self.config.get('alpha', 0.3)
        self.risk_multiplier = self.config.get('risk_multiplier', 1.5)
        self.max_position = self.config.get('max_position', Decimal('0.05'))  # 5% max position
        self.min_spread = self.config.get('min_spread', 0.0001)  # 0.01% minimum spread
        self.base_size = self.config.get('base_size', Decimal('0.01'))  # 1% base position
        
        # Momentum detection
        self.momentum_window_short = 5  # 5 seconds
        self.momentum_window_medium = 15  # 15 seconds
        self.momentum_window_long = 60  # 1 minute
        self.momentum_threshold = 0.0005  # 0.05% momentum threshold
        
        # Order book analysis
        self.order_book_depth = 10  # Analyze top 10 levels
        self.imbalance_threshold = 0.7  # 70% imbalance threshold
        
        # Timing and execution
        self.signal_timeout = 0.5  # 500ms signal timeout
        self.execution_window = 0.1  # 100ms execution window
        self.latency_threshold = 0.01  # 10ms max latency
        
        # Performance tracking
        self.price_history = deque(maxlen=3600)  # 1 hour of second-by-second data
        self.volume_history = deque(maxlen=300)  # 5 minutes of volume data
        self.spread_history = deque(maxlen=300)  # 5 minutes of spread data
        
        # Risk management
        self.max_trades_per_minute = 20
        self.trade_count_window = deque(maxlen=60)  # Track trades per second
        self.pnl_threshold = Decimal('0.02')  # 2% P&L threshold
        
        # Market regime detection
        self.volatility_lookback = 300  # 5 minutes for volatility calculation
        self.regime_states = ['low_vol', 'normal', 'high_vol', 'extreme']
        self.current_regime = 'normal'
        
        logger.info("HFTMomentumStrategy initialized for ultra-fast trading")
    
    async def analyze_market(self, market_data: Dict) -> List[TradeSignal]:
        """
        HFT-optimized market analysis with microsecond timing
        """
        try:
            current_time = datetime.now()
            
            # Convert to MarketContext if needed
            if isinstance(market_data, dict):
                market_context = self._create_market_context(market_data)
            else:
                market_context = market_data
            
            # Extract momentum features
            momentum_features = await self._extract_momentum_features(market_context)
            
            # Detect market regime
            regime = await self._detect_market_regime(momentum_features, market_context)
            
            # Generate HFT signals
            signals = await self._generate_hft_signals(momentum_features, market_context, regime)
            
            # Apply HFT-specific risk management
            filtered_signals = await self._apply_hft_risk_management(signals, market_context)
            
            # Apply execution timing optimization
            optimized_signals = await self._optimize_execution_timing(filtered_signals, market_context)
            
            if optimized_signals:
                logger.debug(f"🚀 [HFT-MOMENTUM] Generated {len(optimized_signals)} HFT signals")
            
            return optimized_signals
            
        except Exception as e:
            logger.error(f"Error in HFT momentum analysis: {e}")
            return []
    
    def _create_market_context(self, market_data: Dict) -> MarketContext:
        """Create MarketContext from market data"""
        return MarketContext(
            price_data=market_data.get('price_data', {}),
            order_books=market_data.get('order_books', {}),
            sentiment_data=market_data.get('sentiment_data', {}),
            technical_indicators=market_data.get('technical_indicators', {}),
            volume_data=market_data.get('volume_data', {}),
            news_data=market_data.get('news_data', []),
            arbitrage_opportunities=market_data.get('arbitrage_opportunities', []),
            portfolio_state=market_data.get('portfolio_state', {}),
            risk_metrics=market_data.get('risk_metrics', {}),
            timestamp=datetime.now()
        )
    
    async def _extract_momentum_features(self, market_context: MarketContext) -> MomentumFeatures:
        """Extract HFT momentum features"""
        try:
            features = MomentumFeatures()
            
            # Get price data
            price_data = market_context.price_data.get('BTC-USD', {})
            if not price_data:
                return features
            
            current_price = float(price_data.get('price', 0))
            if current_price <= 0:
                return features
            
            # Update price history
            self.price_history.append({
                'timestamp': datetime.now(),
                'price': current_price
            })
            
            # Calculate price momentum at different timeframes
            if len(self.price_history) >= 5:
                features.price_momentum_5s = self._calculate_price_momentum(5)
            
            if len(self.price_history) >= 15:
                features.price_momentum_15s = self._calculate_price_momentum(15)
            
            if len(self.price_history) >= 60:
                features.price_momentum_1m = self._calculate_price_momentum(60)
            
            # Volume momentum
            volume_data = market_context.volume_data.get('BTC-USD', {})
            current_volume = float(volume_data.get('volume', 0))
            if current_volume > 0:
                self.volume_history.append({
                    'timestamp': datetime.now(),
                    'volume': current_volume
                })
                features.volume_momentum = self._calculate_volume_momentum()
            
            # Order book analysis
            order_books = market_context.order_books.get('bybit', {}).get('BTC-USD', {})
            if order_books:
                features.order_book_imbalance = self._calculate_order_book_imbalance(order_books)
                features.liquidity_score = self._calculate_liquidity_score(order_books)
                features.microstructure_score = self._calculate_microstructure_score(order_books)
            
            # Spread analysis
            spread = self._calculate_spread(order_books)
            if spread > 0:
                self.spread_history.append({
                    'timestamp': datetime.now(),
                    'spread': spread
                })
                features.spread_momentum = self._calculate_spread_momentum()
            
            # Volatility regime
            features.volatility_regime, features.regime_confidence = self._detect_volatility_regime()
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting momentum features: {e}")
            return MomentumFeatures()
    
    def _calculate_price_momentum(self, lookback_seconds: int) -> float:
        """Calculate price momentum over specified timeframe"""
        try:
            if len(self.price_history) < lookback_seconds:
                return 0.0
            
            recent_prices = list(self.price_history)[-lookback_seconds:]
            if len(recent_prices) < 2:
                return 0.0
            
            start_price = recent_prices[0]['price']
            end_price = recent_prices[-1]['price']
            
            if start_price <= 0:
                return 0.0
            
            momentum = (end_price - start_price) / start_price
            return momentum
            
        except Exception as e:
            logger.debug(f"Error calculating price momentum: {e}")
            return 0.0
    
    def _calculate_volume_momentum(self) -> float:
        """Calculate volume momentum"""
        try:
            if len(self.volume_history) < 2:
                return 0.0
            
            recent_volume = list(self.volume_history)[-30:]  # Last 30 seconds
            if len(recent_volume) < 2:
                return 0.0
            
            recent_avg = np.mean([v['volume'] for v in recent_volume])
            historical_avg = np.mean([v['volume'] for v in self.volume_history])
            
            if historical_avg <= 0:
                return 0.0
            
            volume_momentum = (recent_avg - historical_avg) / historical_avg
            return volume_momentum
            
        except Exception as e:
            logger.debug(f"Error calculating volume momentum: {e}")
            return 0.0
    
    def _calculate_order_book_imbalance(self, order_book: Dict) -> float:
        """Calculate order book imbalance"""
        try:
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])
            
            if not bids or not asks:
                return 0.0
            
            # Calculate weighted bid/ask volumes
            bid_volume = sum(float(bid[1]) for bid in bids[:self.order_book_depth])
            ask_volume = sum(float(ask[1]) for ask in asks[:self.order_book_depth])
            
            total_volume = bid_volume + ask_volume
            if total_volume <= 0:
                return 0.0
            
            imbalance = (bid_volume - ask_volume) / total_volume
            return imbalance
            
        except Exception as e:
            logger.debug(f"Error calculating order book imbalance: {e}")
            return 0.0
    
    def _calculate_liquidity_score(self, order_book: Dict) -> float:
        """Calculate liquidity score"""
        try:
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])
            
            if not bids or not asks:
                return 0.0
            
            # Total volume in top levels
            total_volume = (
                sum(float(bid[1]) for bid in bids[:5]) +
                sum(float(ask[1]) for ask in asks[:5])
            )
            
            # Spread
            spread = float(asks[0][0]) - float(bids[0][0])
            mid_price = (float(asks[0][0]) + float(bids[0][0])) / 2
            spread_pct = spread / mid_price if mid_price > 0 else 1.0
            
            # Liquidity score (higher volume, lower spread = better liquidity)
            liquidity_score = total_volume / (1 + spread_pct * 1000)
            
            # Normalize to 0-1 range
            return min(liquidity_score / 1000000, 1.0)
            
        except Exception as e:
            logger.debug(f"Error calculating liquidity score: {e}")
            return 0.0
    
    def _calculate_microstructure_score(self, order_book: Dict) -> float:
        """Calculate microstructure quality score"""
        try:
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])
            
            if len(bids) < 5 or len(asks) < 5:
                return 0.0
            
            # Price clustering (lower is better)
            bid_spreads = [float(bids[i][0]) - float(bids[i+1][0]) for i in range(4)]
            ask_spreads = [float(asks[i+1][0]) - float(asks[i][0]) for i in range(4)]
            
            avg_bid_spread = np.mean(bid_spreads)
            avg_ask_spread = np.mean(ask_spreads)
            
            # Volume distribution (more even is better)
            bid_volumes = [float(bid[1]) for bid in bids[:5]]
            ask_volumes = [float(ask[1]) for ask in asks[:5]]
            
            bid_volume_cv = np.std(bid_volumes) / np.mean(bid_volumes) if np.mean(bid_volumes) > 0 else 1.0
            ask_volume_cv = np.std(ask_volumes) / np.mean(ask_volumes) if np.mean(ask_volumes) > 0 else 1.0
            
            # Combined microstructure score
            spread_score = 1.0 / (1.0 + avg_bid_spread + avg_ask_spread)
            volume_score = 1.0 / (1.0 + bid_volume_cv + ask_volume_cv)
            
            microstructure_score = (spread_score + volume_score) / 2
            return microstructure_score
            
        except Exception as e:
            logger.debug(f"Error calculating microstructure score: {e}")
            return 0.0
    
    def _calculate_spread(self, order_book: Dict) -> float:
        """Calculate bid-ask spread"""
        try:
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])
            
            if not bids or not asks:
                return 0.0
            
            best_bid = float(bids[0][0])
            best_ask = float(asks[0][0])
            
            spread = best_ask - best_bid
            mid_price = (best_ask + best_bid) / 2
            
            return spread / mid_price if mid_price > 0 else 0.0
            
        except Exception as e:
            logger.debug(f"Error calculating spread: {e}")
            return 0.0
    
    def _calculate_spread_momentum(self) -> float:
        """Calculate spread momentum"""
        try:
            if len(self.spread_history) < 10:
                return 0.0
            
            recent_spreads = [s['spread'] for s in list(self.spread_history)[-10:]]
            historical_spreads = [s['spread'] for s in self.spread_history]
            
            recent_avg = np.mean(recent_spreads)
            historical_avg = np.mean(historical_spreads)
            
            if historical_avg <= 0:
                return 0.0
            
            spread_momentum = (recent_avg - historical_avg) / historical_avg
            return -spread_momentum  # Negative because lower spread is better
            
        except Exception as e:
            logger.debug(f"Error calculating spread momentum: {e}")
            return 0.0
    
    def _detect_volatility_regime(self) -> Tuple[str, float]:
        """Detect current volatility regime"""
        try:
            if len(self.price_history) < self.volatility_lookback:
                return 'normal', 0.5
            
            recent_prices = [p['price'] for p in list(self.price_history)[-self.volatility_lookback:]]
            returns = np.diff(np.log(recent_prices))
            volatility = np.std(returns) * np.sqrt(300)  # Annualized 5-minute volatility
            
            # Volatility thresholds
            if volatility < 0.5:
                regime = 'low_vol'
                confidence = 0.8
            elif volatility < 1.0:
                regime = 'normal'
                confidence = 0.9
            elif volatility < 2.0:
                regime = 'high_vol'
                confidence = 0.8
            else:
                regime = 'extreme'
                confidence = 0.7
            
            self.current_regime = regime
            return regime, confidence
            
        except Exception as e:
            logger.debug(f"Error detecting volatility regime: {e}")
            return 'normal', 0.5
    
    async def _detect_market_regime(self, features: MomentumFeatures, market_context: MarketContext) -> str:
        """Detect current market regime for strategy adaptation"""
        try:
            # Combine multiple signals for regime detection
            momentum_strength = abs(features.price_momentum_15s)
            volume_strength = features.volume_momentum
            imbalance_strength = abs(features.order_book_imbalance)
            
            # Regime classification
            if momentum_strength > 0.002 and volume_strength > 0.5:
                if features.order_book_imbalance > 0.3:
                    regime = 'strong_bullish_momentum'
                elif features.order_book_imbalance < -0.3:
                    regime = 'strong_bearish_momentum'
                else:
                    regime = 'neutral_momentum'
            elif momentum_strength < 0.0005 and volume_strength < 0.2:
                regime = 'range_bound'
            elif imbalance_strength > 0.6:
                regime = 'order_book_dominated'
            else:
                regime = 'normal_trading'
            
            return regime
            
        except Exception as e:
            logger.debug(f"Error detecting market regime: {e}")
            return 'normal_trading'
    
    async def _generate_hft_signals(self, features: MomentumFeatures, 
                                   market_context: MarketContext, regime: str) -> List[TradeSignal]:
        """Generate HFT momentum signals"""
        try:
            signals = []
            current_time = datetime.now()
            
            # Check if we should generate signals based on momentum
            if not self._should_generate_signal(features, regime):
                return signals
            
            # Determine signal direction and strength
            signal_direction = self._determine_signal_direction(features, regime)
            signal_confidence = self._calculate_signal_confidence(features, regime)
            
            if signal_direction == 'HOLD' or signal_confidence < 0.6:
                return signals
            
            # Calculate position size based on regime and features
            position_size = self._calculate_hft_position_size(features, regime, signal_confidence)
            
            if position_size <= 0:
                return signals
            
            # Create HFT signal
            signal = TradeSignal(
                symbol='BTC-USD',
                action=signal_direction,
                amount=float(position_size),
                confidence=signal_confidence,
                metadata={
                    'strategy': 'hft_momentum',
                    'regime': regime,
                    'features': {
                        'momentum_5s': features.price_momentum_5s,
                        'momentum_15s': features.price_momentum_15s,
                        'volume_momentum': features.volume_momentum,
                        'order_book_imbalance': features.order_book_imbalance,
                        'liquidity_score': features.liquidity_score
                    },
                    'execution_window': self.execution_window,
                    'timestamp': current_time.isoformat()
                }
            )
            
            signals.append(signal)
            
            logger.debug(f"🚀 [HFT-SIGNAL] {signal_direction} BTC-USD "
                        f"(confidence: {signal_confidence:.3f}, size: {position_size:.4f}, regime: {regime})")
            
            return signals
            
        except Exception as e:
            logger.error(f"Error generating HFT signals: {e}")
            return []
    
    def _should_generate_signal(self, features: MomentumFeatures, regime: str) -> bool:
        """Determine if conditions are right for signal generation"""
        try:
            # Check basic momentum threshold
            if abs(features.price_momentum_15s) < self.momentum_threshold:
                return False
            
            # Check liquidity conditions
            if features.liquidity_score < 0.3:
                return False
            
            # Check volatility regime
            if features.volatility_regime == 'extreme':
                return False
            
            # Check recent trading frequency
            recent_trades = len([t for t in self.trade_count_window 
                               if (datetime.now() - t).total_seconds() < 60])
            if recent_trades >= self.max_trades_per_minute:
                return False
            
            return True
            
        except Exception as e:
            logger.debug(f"Error checking signal generation conditions: {e}")
            return False
    
    def _determine_signal_direction(self, features: MomentumFeatures, regime: str) -> str:
        """Determine signal direction based on features and regime"""
        try:
            # Primary momentum signal
            momentum_signal = 'BUY' if features.price_momentum_15s > 0 else 'SELL'
            
            # Order book confirmation
            order_book_signal = 'BUY' if features.order_book_imbalance > 0.1 else 'SELL'
            
            # Volume confirmation
            volume_signal = 'BUY' if features.volume_momentum > 0.2 else 'HOLD'
            
            # Regime-specific adjustments
            if regime == 'strong_bullish_momentum':
                return 'BUY'
            elif regime == 'strong_bearish_momentum':
                return 'SELL'
            elif regime == 'range_bound':
                # Use mean reversion in range-bound markets
                if abs(features.price_momentum_15s) > 0.001:
                    return 'SELL' if features.price_momentum_15s > 0 else 'BUY'
                else:
                    return 'HOLD'
            else:
                # Normal trading - use momentum
                if momentum_signal == order_book_signal and volume_signal != 'HOLD':
                    return momentum_signal
                else:
                    return 'HOLD'
            
        except Exception as e:
            logger.debug(f"Error determining signal direction: {e}")
            return 'HOLD'
    
    def _calculate_signal_confidence(self, features: MomentumFeatures, regime: str) -> float:
        """Calculate signal confidence based on feature alignment"""
        try:
            confidence_factors = []
            
            # Momentum strength
            momentum_strength = abs(features.price_momentum_15s) / 0.002  # Normalize to 0.2%
            confidence_factors.append(min(momentum_strength, 1.0))
            
            # Volume confirmation
            volume_confidence = min(abs(features.volume_momentum) / 0.5, 1.0)
            confidence_factors.append(volume_confidence)
            
            # Order book alignment
            imbalance_confidence = min(abs(features.order_book_imbalance) / 0.7, 1.0)
            confidence_factors.append(imbalance_confidence)
            
            # Liquidity quality
            confidence_factors.append(features.liquidity_score)
            
            # Microstructure quality
            confidence_factors.append(features.microstructure_score)
            
            # Regime confidence
            confidence_factors.append(features.regime_confidence)
            
            # Calculate weighted average confidence
            weights = [0.3, 0.2, 0.2, 0.1, 0.1, 0.1]  # Momentum gets highest weight
            confidence = sum(f * w for f, w in zip(confidence_factors, weights))
            
            # Regime-specific adjustments
            if regime == 'strong_bullish_momentum' or regime == 'strong_bearish_momentum':
                confidence *= 1.2  # Boost confidence in strong momentum regimes
            elif regime == 'range_bound':
                confidence *= 0.8  # Reduce confidence in range-bound markets
            
            return min(max(confidence, 0.0), 1.0)
            
        except Exception as e:
            logger.debug(f"Error calculating signal confidence: {e}")
            return 0.5
    
    def _calculate_hft_position_size(self, features: MomentumFeatures, 
                                   regime: str, confidence: float) -> Decimal:
        """Calculate optimal position size for HFT strategy"""
        try:
            # Base position size
            base_size = self.base_size
            
            # Adjust for confidence
            confidence_multiplier = confidence ** 2  # Square for more aggressive scaling
            
            # Adjust for momentum strength
            momentum_multiplier = min(abs(features.price_momentum_15s) / 0.001, 2.0)
            
            # Adjust for liquidity
            liquidity_multiplier = features.liquidity_score
            
            # Adjust for volatility regime
            volatility_multipliers = {
                'low_vol': 1.5,
                'normal': 1.0,
                'high_vol': 0.7,
                'extreme': 0.3
            }
            volatility_multiplier = volatility_multipliers.get(features.volatility_regime, 1.0)
            
            # Calculate final position size
            position_size = (base_size * 
                           confidence_multiplier * 
                           momentum_multiplier * 
                           liquidity_multiplier * 
                           volatility_multiplier)
            
            # Apply maximum position limit
            position_size = min(position_size, self.max_position)
            
            # Apply minimum viable size
            min_size = Decimal('0.001')  # 0.1% minimum
            if position_size < min_size:
                return Decimal('0')
            
            return position_size
            
        except Exception as e:
            logger.debug(f"Error calculating HFT position size: {e}")
            return Decimal('0')
    
    async def _apply_hft_risk_management(self, signals: List[TradeSignal], 
                                       market_context: MarketContext) -> List[TradeSignal]:
        """Apply HFT-specific risk management"""
        try:
            filtered_signals = []
            
            for signal in signals:
                # Check spread requirements
                order_books = market_context.order_books.get('bybit', {}).get('BTC-USD', {})
                if order_books:
                    spread = self._calculate_spread(order_books)
                    if spread > self.min_spread * 10:  # 10x minimum spread is too wide
                        logger.debug(f"🚨 [HFT-RISK] Signal rejected - spread too wide: {spread:.6f}")
                        continue
                
                # Check latency requirements (placeholder - would integrate with exchange latency monitoring)
                estimated_latency = 0.005  # 5ms placeholder
                if estimated_latency > self.latency_threshold:
                    logger.debug(f"🚨 [HFT-RISK] Signal rejected - latency too high: {estimated_latency:.3f}s")
                    continue
                
                # Check if signal is still fresh
                signal_age = (datetime.now() - datetime.fromisoformat(signal.metadata['timestamp'])).total_seconds()
                if signal_age > self.signal_timeout:
                    logger.debug(f"🚨 [HFT-RISK] Signal rejected - too old: {signal_age:.3f}s")
                    continue
                
                filtered_signals.append(signal)
            
            return filtered_signals
            
        except Exception as e:
            logger.error(f"Error applying HFT risk management: {e}")
            return signals
    
    async def _optimize_execution_timing(self, signals: List[TradeSignal], 
                                       market_context: MarketContext) -> List[TradeSignal]:
        """Optimize execution timing for HFT signals"""
        try:
            optimized_signals = []
            
            for signal in signals:
                # Add execution timing metadata
                signal.metadata['execution_priority'] = 'immediate'
                signal.metadata['max_execution_delay'] = self.execution_window
                signal.metadata['order_type'] = 'market'  # HFT typically uses market orders
                signal.metadata['time_in_force'] = 'IOC'  # Immediate or Cancel
                
                # Record trade for frequency tracking
                self.trade_count_window.append(datetime.now())
                
                optimized_signals.append(signal)
            
            return optimized_signals
            
        except Exception as e:
            logger.error(f"Error optimizing execution timing: {e}")
            return signals
